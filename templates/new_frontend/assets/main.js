// 全局JavaScript功能和交互效果

// 工具函数
const Utils = {
    // 防抖函数
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // 节流函数
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        }
    },

    // 格式化数字
    formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    },

    // 格式化日期
    formatDate(date) {
        const now = new Date();
        const diff = now - new Date(date);
        const days = Math.floor(diff / (1000 * 60 * 60 * 24));
        
        if (days === 0) return '今天';
        if (days === 1) return '昨天';
        if (days < 7) return `${days}天前`;
        if (days < 30) return `${Math.floor(days / 7)}周前`;
        if (days < 365) return `${Math.floor(days / 30)}个月前`;
        return `${Math.floor(days / 365)}年前`;
    },

    // 复制到剪贴板
    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            this.showNotification('已复制到剪贴板', 'success');
        } catch (err) {
            console.error('复制失败:', err);
            this.showNotification('复制失败', 'error');
        }
    },

    // 显示通知
    showNotification(message, type = 'info', duration = 3000) {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="flex items-center">
                <i class="fas ${this.getNotificationIcon(type)} mr-2"></i>
                <span>${message}</span>
                <button class="ml-auto text-white hover:text-gray-200" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        const container = document.getElementById('notification-container') || this.createNotificationContainer();
        container.appendChild(notification);

        setTimeout(() => {
            if (notification.parentElement) {
                notification.style.opacity = '0';
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => notification.remove(), 300);
            }
        }, duration);
    },

    getNotificationIcon(type) {
        const icons = {
            success: 'fa-check-circle',
            error: 'fa-exclamation-circle',
            warning: 'fa-exclamation-triangle',
            info: 'fa-info-circle'
        };
        return icons[type] || icons.info;
    },

    createNotificationContainer() {
        const container = document.createElement('div');
        container.id = 'notification-container';
        container.className = 'fixed top-4 right-4 z-50 space-y-2';
        document.body.appendChild(container);
        return container;
    }
};

// 页面加载动画
class PageLoader {
    constructor() {
        this.init();
    }

    init() {
        // 创建加载器
        this.createLoader();
        
        // 页面加载完成后隐藏加载器
        window.addEventListener('load', () => {
            this.hideLoader();
        });
    }

    createLoader() {
        const loader = document.createElement('div');
        loader.id = 'page-loader';
        loader.className = 'fixed inset-0 bg-white z-50 flex items-center justify-center';
        loader.innerHTML = `
            <div class="text-center">
                <div class="loading-spinner mb-4"></div>
                <p class="text-gray-600">加载中...</p>
            </div>
        `;
        document.body.appendChild(loader);
    }

    hideLoader() {
        const loader = document.getElementById('page-loader');
        if (loader) {
            loader.style.opacity = '0';
            setTimeout(() => loader.remove(), 300);
        }
    }
}

// 滚动效果管理
class ScrollEffects {
    constructor() {
        this.init();
    }

    init() {
        this.setupScrollToTop();
        this.setupScrollProgress();
        this.setupScrollAnimations();
    }

    setupScrollToTop() {
        const button = document.createElement('button');
        button.id = 'scroll-to-top';
        button.className = 'fixed bottom-6 right-6 bg-blue-600 text-white p-3 rounded-full shadow-lg opacity-0 transition-all duration-300 hover:bg-blue-700 z-40';
        button.innerHTML = '<i class="fas fa-arrow-up"></i>';
        button.style.transform = 'translateY(100px)';
        
        button.addEventListener('click', () => {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });

        document.body.appendChild(button);

        window.addEventListener('scroll', Utils.throttle(() => {
            if (window.pageYOffset > 300) {
                button.style.opacity = '1';
                button.style.transform = 'translateY(0)';
            } else {
                button.style.opacity = '0';
                button.style.transform = 'translateY(100px)';
            }
        }, 100));
    }

    setupScrollProgress() {
        const progress = document.createElement('div');
        progress.className = 'fixed top-0 left-0 h-1 bg-blue-600 z-50 transition-all duration-300';
        progress.style.width = '0%';
        document.body.appendChild(progress);

        window.addEventListener('scroll', Utils.throttle(() => {
            const scrolled = (window.pageYOffset / (document.documentElement.scrollHeight - window.innerHeight)) * 100;
            progress.style.width = `${Math.min(scrolled, 100)}%`;
        }, 10));
    }

    setupScrollAnimations() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-fade-in-up');
                }
            });
        }, { threshold: 0.1 });

        // 观察所有卡片元素
        document.querySelectorAll('.card, .metric-card, .page-section').forEach(el => {
            observer.observe(el);
        });
    }
}

// 表单增强
class FormEnhancer {
    constructor() {
        this.init();
    }

    init() {
        this.setupFormValidation();
        this.setupInputEffects();
        this.setupFileUpload();
    }

    setupFormValidation() {
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', (e) => {
                if (!this.validateForm(form)) {
                    e.preventDefault();
                }
            });
        });
    }

    validateForm(form) {
        let isValid = true;
        const inputs = form.querySelectorAll('input[required], textarea[required], select[required]');
        
        inputs.forEach(input => {
            if (!input.value.trim()) {
                this.showFieldError(input, '此字段为必填项');
                isValid = false;
            } else {
                this.clearFieldError(input);
            }
        });

        return isValid;
    }

    showFieldError(input, message) {
        this.clearFieldError(input);
        
        const error = document.createElement('div');
        error.className = 'field-error text-red-500 text-sm mt-1';
        error.textContent = message;
        
        input.parentNode.appendChild(error);
        input.classList.add('border-red-500');
    }

    clearFieldError(input) {
        const error = input.parentNode.querySelector('.field-error');
        if (error) {
            error.remove();
        }
        input.classList.remove('border-red-500');
    }

    setupInputEffects() {
        document.querySelectorAll('input, textarea').forEach(input => {
            input.addEventListener('focus', () => {
                input.parentNode.classList.add('input-focused');
            });

            input.addEventListener('blur', () => {
                input.parentNode.classList.remove('input-focused');
            });
        });
    }

    setupFileUpload() {
        document.querySelectorAll('input[type="file"]').forEach(input => {
            input.addEventListener('change', (e) => {
                const files = e.target.files;
                if (files.length > 0) {
                    const fileName = files[0].name;
                    const label = input.parentNode.querySelector('label') || input.nextElementSibling;
                    if (label) {
                        label.textContent = `已选择: ${fileName}`;
                    }
                }
            });
        });
    }
}

// 搜索功能增强
class SearchEnhancer {
    constructor() {
        this.init();
    }

    init() {
        this.setupSearchInputs();
        this.setupSearchHistory();
    }

    setupSearchInputs() {
        document.querySelectorAll('input[type="search"], .search-input').forEach(input => {
            const searchHandler = Utils.debounce((query) => {
                this.performSearch(query, input);
            }, 300);

            input.addEventListener('input', (e) => {
                searchHandler(e.target.value);
            });
        });
    }

    performSearch(query, input) {
        if (query.length < 2) return;

        // 保存搜索历史
        this.saveSearchHistory(query);

        // 触发搜索事件
        const event = new CustomEvent('search', { detail: { query, input } });
        document.dispatchEvent(event);
    }

    saveSearchHistory(query) {
        let history = JSON.parse(localStorage.getItem('searchHistory') || '[]');
        history = history.filter(item => item !== query);
        history.unshift(query);
        history = history.slice(0, 10); // 只保留最近10条
        localStorage.setItem('searchHistory', JSON.stringify(history));
    }

    getSearchHistory() {
        return JSON.parse(localStorage.getItem('searchHistory') || '[]');
    }

    setupSearchHistory() {
        document.addEventListener('click', (e) => {
            if (e.target.matches('.search-input')) {
                this.showSearchSuggestions(e.target);
            }
        });
    }

    showSearchSuggestions(input) {
        const history = this.getSearchHistory();
        if (history.length === 0) return;

        const suggestions = document.createElement('div');
        suggestions.className = 'search-suggestions absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-lg shadow-lg mt-1 z-10';
        
        history.forEach(item => {
            const suggestion = document.createElement('div');
            suggestion.className = 'p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0';
            suggestion.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-history text-gray-400 mr-2"></i>
                    <span>${item}</span>
                </div>
            `;
            
            suggestion.addEventListener('click', () => {
                input.value = item;
                input.dispatchEvent(new Event('input'));
                suggestions.remove();
            });
            
            suggestions.appendChild(suggestion);
        });

        // 移除现有建议
        const existing = input.parentNode.querySelector('.search-suggestions');
        if (existing) existing.remove();

        input.parentNode.style.position = 'relative';
        input.parentNode.appendChild(suggestions);

        // 点击外部关闭建议
        setTimeout(() => {
            document.addEventListener('click', function closeSuggestions(e) {
                if (!suggestions.contains(e.target) && e.target !== input) {
                    suggestions.remove();
                    document.removeEventListener('click', closeSuggestions);
                }
            });
        }, 0);
    }
}

// 主题切换
class ThemeManager {
    constructor() {
        this.currentTheme = localStorage.getItem('theme') || 'light';
        this.init();
    }

    init() {
        this.applyTheme(this.currentTheme);
        this.setupThemeToggle();
    }

    applyTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        if (theme === 'dark') {
            document.documentElement.classList.add('dark');
        } else {
            document.documentElement.classList.remove('dark');
        }
        localStorage.setItem('theme', theme);
        this.currentTheme = theme;
    }

    toggleTheme() {
        const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.applyTheme(newTheme);
    }

    setupThemeToggle() {
        document.querySelectorAll('.theme-toggle').forEach(button => {
            button.addEventListener('click', () => {
                this.toggleTheme();
            });
        });
    }
}

// 性能监控
class PerformanceMonitor {
    constructor() {
        this.init();
    }

    init() {
        this.monitorPageLoad();
        this.monitorUserInteractions();
    }

    monitorPageLoad() {
        window.addEventListener('load', () => {
            const loadTime = performance.now();
            console.log(`页面加载时间: ${loadTime.toFixed(2)}ms`);
            
            // 可以发送到分析服务
            this.sendAnalytics('page_load', { loadTime });
        });
    }

    monitorUserInteractions() {
        document.addEventListener('click', (e) => {
            if (e.target.matches('button, a, .clickable')) {
                const element = e.target.tagName.toLowerCase();
                const text = e.target.textContent.trim().substring(0, 50);
                this.sendAnalytics('user_click', { element, text });
            }
        });
    }

    sendAnalytics(event, data) {
        // 这里可以发送到实际的分析服务
        console.log('Analytics:', event, data);
    }
}

// 初始化所有功能
document.addEventListener('DOMContentLoaded', () => {
    // 初始化各个模块
    new PageLoader();
    new ScrollEffects();
    new FormEnhancer();
    new SearchEnhancer();
    new ThemeManager();
    new PerformanceMonitor();

    // 全局事件监听
    document.addEventListener('keydown', (e) => {
        // ESC键关闭模态框
        if (e.key === 'Escape') {
            document.querySelectorAll('.modal, .dropdown-open').forEach(el => {
                el.classList.remove('show', 'dropdown-open');
            });
        }
    });

    // 图片懒加载
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });

        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
    }

    // 添加页面可见性API支持
    document.addEventListener('visibilitychange', () => {
        if (document.hidden) {
            console.log('页面隐藏');
        } else {
            console.log('页面可见');
        }
    });
});

// 导出工具函数供全局使用
window.Utils = Utils;
