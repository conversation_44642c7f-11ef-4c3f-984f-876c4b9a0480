# 功能更新说明

## 📋 更新概述

根据最新的功能需求，我们对项目调研报告平台的前端页面进行了全面调整，以满足以下核心功能：

1. **访客默认进入调研报告列表页**
2. **分页展示已有的调研报告列表**
3. **提供两种查看方式：分析页面和分析报告**
4. **搜索和项目申请功能**

## 🔄 主要变更

### 1. 首页改造 (home.html)

#### 原有设计
- 卡片式展示报告
- 重点展示项目图片和描述
- 简单的操作按钮

#### 新设计
- **表格式列表展示**，包含以下信息：
  - 项目名称（带图标）
  - 创建人
  - 创建时间
  - 更新时间
  - 状态（已完成）
  - 操作按钮

#### 新增功能
- **搜索框**：支持项目名模糊查询（大小写不敏感）
- **「找不到项目？」链接**：点击显示项目申请弹窗
- **信息收集弹窗**：收集用户邮箱、项目名称、官网链接
- **提示信息**：显示"请提交你想要调研的项目，算力因素报告产出可能会有延迟，完成后我们会通过邮箱通知，谢谢！"

#### 操作按钮
- **查看分析页面**：跳转到交互式HTML分析页面
- **查看分析报告**：跳转到Markdown报告页面

### 2. 分析页面改造 (analysis.html)

#### 功能定位
专门用于渲染项目的**交互式HTML分析文件**

#### 主要特性
- 通过iframe嵌入HTML分析文件
- 自动调整iframe高度
- 支持跨域内容展示
- 提供加载状态和错误处理
- 支持分析页面导出和分享

#### 技术实现
```javascript
// 动态加载HTML文件
htmlFilePath: 'demo-analysis-react.html',

// iframe自适应高度
onFrameLoad() {
    const frameDoc = frame.contentDocument || frame.contentWindow.document;
    const height = frameDoc.body.scrollHeight;
    frame.style.height = Math.max(height, 800) + 'px';
}
```

### 3. 报告详情页改造 (report-detail.html)

#### 功能定位
专门用于渲染项目的**Markdown报告文件**

#### 主要特性
- 动态加载Markdown文件内容
- 使用marked.js库解析Markdown
- 美观的Markdown样式展示
- 支持报告下载和分享
- 显示详细的报告元信息

#### 技术实现
```javascript
// 加载Markdown内容
async loadMarkdownContent() {
    const response = await fetch(`/api/report/${this.reportId}/markdown`);
    const markdownText = await response.text();
    this.markdownContent = marked.parse(markdownText);
}
```

### 4. 项目申请功能

#### 触发方式
- 首页搜索栏旁的「找不到项目？」链接
- 搜索无结果时的申请按钮

#### 收集信息
- **用户邮箱**（必填）
- **项目名称**（必填）
- **官网主页链接**（必填）
- **补充说明**（可选）

#### 处理流程
1. 用户提交申请
2. 信息记录到数据库
3. 通知维护方有新申请
4. 维护方处理并上传报告
5. 邮件通知用户报告已完成

## 🛠️ 技术架构

### 前端技术栈
- **HTML5 + CSS3**：页面结构和样式
- **Tailwind CSS**：原子化CSS框架
- **Alpine.js**：轻量级JavaScript框架
- **FontAwesome**：图标库
- **Chart.js**：图表可视化（用于演示HTML文件）
- **marked.js**：Markdown解析库

### 数据流设计

#### 报告列表页面
```
用户访问 → 加载报告列表 → 显示表格 → 用户操作
                ↓
        [查看分析页面] → analysis.html → 加载HTML文件
                ↓
        [查看分析报告] → report-detail.html → 加载Markdown文件
```

#### 项目申请流程
```
用户点击「找不到项目？」→ 显示申请弹窗 → 填写信息 → 提交申请
                                    ↓
                            记录数据库 → 通知维护方 → 处理申请 → 邮件通知
```

### API接口设计

#### 获取报告列表
```
GET /api/reports?page=1&search=react
Response: {
    reports: [...],
    total: 100,
    page: 1
}
```

#### 获取报告基本信息
```
GET /api/report/{id}
Response: {
    id: 1,
    name: "React.js",
    creator: "张三",
    created_at: "2024-01-15",
    updated_at: "2024-01-20"
}
```

#### 获取Markdown内容
```
GET /api/report/{id}/markdown
Response: "# 项目调研报告\n\n## 概述\n..."
```

#### 获取HTML分析文件路径
```
GET /api/report/{id}/analysis
Response: {
    html_path: "reports/react_analysis.html",
    name: "React.js",
    last_updated: "2024-01-20"
}
```

#### 提交项目申请
```
POST /api/request-project
Body: {
    email: "<EMAIL>",
    project_name: "Vue.js",
    website: "https://vuejs.org",
    description: "希望了解Vue.js的技术特性"
}
```

## 📁 文件结构

```
templates/new_frontend/
├── index.html                    # 主入口页面（原型展示）
├── assets/
│   ├── styles.css               # 全局样式
│   └── main.js                  # 交互功能
├── components/
│   ├── home.html                # 调研报告列表页
│   ├── report-detail.html       # Markdown报告页
│   ├── analysis.html            # 交互式分析页
│   ├── project-request.html     # 项目申请页（保留）
│   ├── admin-login.html         # 管理员登录页
│   ├── admin-dashboard.html     # 管理员仪表板
│   └── demo-analysis-react.html # 演示用HTML分析文件
├── integration_guide.py         # Flask集成指南
├── README.md                    # 使用说明
└── FEATURE_UPDATE.md           # 功能更新说明（本文件）
```

## 🎯 核心功能实现

### 1. 表格式报告列表
- 响应式表格设计
- 支持排序和筛选
- 美观的状态标识
- 清晰的操作按钮

### 2. 双重查看模式
- **分析页面**：iframe渲染HTML文件，支持交互式内容
- **分析报告**：动态解析Markdown文件，美观展示

### 3. 智能搜索功能
- 实时搜索（防抖处理）
- 大小写不敏感
- 支持项目名模糊匹配
- 搜索历史记录

### 4. 项目申请流程
- 弹窗式信息收集
- 表单验证和提交
- 成功反馈和状态管理
- 后台处理工作流

## 🚀 部署和集成

### 1. 直接使用
将`components/`目录下的HTML文件直接部署到Web服务器

### 2. Flask集成
参考`integration_guide.py`中的示例代码进行集成

### 3. API对接
实现相应的后端API接口以支持动态数据加载

### 4. 文件管理
- HTML分析文件存储在指定目录
- Markdown报告文件存储在指定目录
- 通过数据库记录文件路径

## 📈 性能优化

### 1. 前端优化
- 懒加载iframe内容
- 防抖搜索请求
- 组件化代码结构
- 响应式图片加载

### 2. 后端优化
- API接口缓存
- 文件读取优化
- 数据库查询优化
- CDN静态资源加速

## 🔧 维护和扩展

### 1. 添加新报告
1. 上传HTML分析文件到指定目录
2. 上传Markdown报告文件到指定目录
3. 在数据库中创建报告记录
4. 更新文件路径信息

### 2. 自定义样式
修改`assets/styles.css`中的CSS变量和样式规则

### 3. 扩展功能
在`assets/main.js`中添加新的JavaScript功能

### 4. 国际化支持
添加多语言支持，修改文本内容和日期格式

---

**更新时间**：2024-01-20  
**版本**：v2.0.0  
**维护者**：开发团队
