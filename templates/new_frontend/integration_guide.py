# Flask集成指南 - 如何将新前端页面集成到现有项目

"""
这个文件展示了如何将新设计的前端页面集成到现有的Flask应用中。
包含了路由配置、模板渲染和数据传递的示例代码。
"""

from flask import Flask, render_template, request, jsonify, redirect, url_for
from flask_login import login_required, current_user
import json
from datetime import datetime

app = Flask(__name__)

# ============================================================================
# 公共页面路由 (对应 components/ 目录下的页面)
# ============================================================================

@app.route('/')
def index():
    """
    首页 - 调研报告列表页
    显示项目名、创建时间、更新时间、创建人等信息
    提供搜索功能和项目申请入口
    """
    page = request.args.get('page', 1, type=int)
    search_query = request.args.get('search', '').strip()

    # 从现有的ResearchReport模型获取数据
    # reports, total_count = ResearchReport.get_published_reports(
    #     page=page, per_page=10, search_query=search_query
    # )

    # 模拟数据 - 实际使用时替换为真实数据
    reports = [
        {
            'id': 1,
            'name': 'React.js',
            'creator': '张三',
            'created_at': '2024-01-15',
            'updated_at': '2024-01-20',
            'category': '前端框架',
            'website': 'https://reactjs.org',
            'status': 'completed',
            'html_file_path': 'reports/react_analysis.html',  # 交互式分析HTML文件路径
            'markdown_file_path': 'reports/react_report.md'   # Markdown报告文件路径
        },
        {
            'id': 2,
            'name': 'Vue.js',
            'creator': '李四',
            'created_at': '2024-01-10',
            'updated_at': '2024-01-18',
            'category': '前端框架',
            'website': 'https://vuejs.org',
            'status': 'completed',
            'html_file_path': 'reports/vue_analysis.html',
            'markdown_file_path': 'reports/vue_report.md'
        }
        # 更多报告数据...
    ]

    return render_template('new_frontend/components/home.html',
                         reports=reports,
                         search_query=search_query,
                         current_page=page)

@app.route('/report/<report_id>')
def report_detail(report_id):
    """
    报告详情页 - 渲染Markdown文件内容
    """
    # report = ResearchReport.get_by_id(report_id)
    # if not report or not report.get('is_published'):
    #     return redirect(url_for('index'))

    return render_template('new_frontend/components/report-detail.html')

@app.route('/report/<report_id>/analysis')
def analysis(report_id):
    """
    数据分析页 - 渲染HTML文件内容
    """
    return render_template('new_frontend/components/analysis.html')

# 新增API端点用于获取报告数据
@app.route('/api/report/<report_id>')
def api_get_report(report_id):
    """
    获取报告基本信息API
    """
    # report = ResearchReport.get_by_id(report_id)

    # 模拟数据
    mock_reports = {
        '1': {
            'id': 1,
            'name': 'React.js',
            'creator': '张三',
            'created_at': '2024-01-15',
            'updated_at': '2024-01-20',
            'category': '前端框架',
            'website': 'https://reactjs.org',
            'status': 'completed'
        },
        '2': {
            'id': 2,
            'name': 'Vue.js',
            'creator': '李四',
            'created_at': '2024-01-10',
            'updated_at': '2024-01-18',
            'category': '前端框架',
            'website': 'https://vuejs.org',
            'status': 'completed'
        }
    }

    report = mock_reports.get(report_id)
    if not report:
        return jsonify({'error': '报告不存在'}), 404

    return jsonify(report)

@app.route('/api/report/<report_id>/markdown')
def api_get_markdown(report_id):
    """
    获取报告Markdown内容API
    """
    # 实际实现中应该从文件系统或数据库读取Markdown文件
    # markdown_path = ResearchReport.get_by_id(report_id).markdown_file_path
    # with open(markdown_path, 'r', encoding='utf-8') as f:
    #     content = f.read()

    # 模拟Markdown内容
    mock_content = f"""# 项目调研报告

## 项目概述
这是一个详细的技术调研报告...

## 技术架构
项目采用现代化的技术架构...

## 性能分析
性能表现优异...

## 总结建议
建议在以下场景中使用...
"""

    return mock_content, 200, {'Content-Type': 'text/plain; charset=utf-8'}

@app.route('/api/report/<report_id>/analysis')
def api_get_analysis_html(report_id):
    """
    获取分析页面HTML文件路径API
    """
    # 实际实现中应该返回HTML文件的路径或内容
    # report = ResearchReport.get_by_id(report_id)
    # html_path = report.html_file_path

    # 模拟HTML文件路径
    mock_html_paths = {
        '1': 'demo-analysis-react.html',
        '2': 'demo-analysis-vue.html',
        '3': 'demo-analysis-node.html'
    }

    html_path = mock_html_paths.get(report_id, 'demo-analysis-react.html')

    return jsonify({
        'html_path': html_path,
        'name': f'项目{report_id}',
        'last_updated': '2024-01-20'
    })

@app.route('/request-project', methods=['GET', 'POST'])
def request_project():
    """
    项目申请页 - 使用新的project-request.html模板
    """
    if request.method == 'POST':
        # 处理表单提交
        form_data = request.get_json() if request.is_json else request.form
        
        # 创建新的用户请求
        # user_request = UserRequest.create({
        #     'user_email': form_data.get('email'),
        #     'project_name': form_data.get('projectName'),
        #     'official_website': form_data.get('website'),
        #     'description': form_data.get('description'),
        #     'status': 'pending'
        # })
        
        if request.is_json:
            return jsonify({'success': True, 'message': '申请提交成功'})
        else:
            return redirect(url_for('index'))
    
    return render_template('new_frontend/components/project-request.html')

# ============================================================================
# 管理员页面路由
# ============================================================================

@app.route('/admin/login', methods=['GET', 'POST'])
def admin_login():
    """
    管理员登录页 - 使用新的admin-login.html模板
    """
    if request.method == 'POST':
        email = request.form.get('email')
        password = request.form.get('password')
        
        # 验证管理员凭据
        # user = AdminUser.get_by_email(email)
        # if user and user.check_password(password):
        #     login_user(user)
        #     return redirect(url_for('admin_dashboard'))
        
        # 模拟登录验证
        if email == '<EMAIL>' and password == 'admin123':
            return jsonify({'success': True, 'redirect': url_for('admin_dashboard')})
        else:
            return jsonify({'success': False, 'error': '邮箱或密码错误'})
    
    return render_template('new_frontend/components/admin-login.html')

@app.route('/admin/dashboard')
@login_required
def admin_dashboard():
    """
    管理员仪表板 - 使用新的admin-dashboard.html模板
    """
    # 获取统计数据
    # stats = {
    #     'total_reports': ResearchReport.get_total_count(),
    #     'pending_requests': UserRequest.get_pending_count(),
    #     'recent_reports': ResearchReport.get_recent_reports(limit=5),
    #     'recent_requests': UserRequest.get_recent_requests(limit=5)
    # }
    
    # 模拟统计数据
    stats = {
        'total_reports': 24,
        'pending_requests': 3,
        'total_views': '12.5K',
        'active_users': 1247,
        'popular_reports': [
            {'name': 'React.js 深度分析', 'views': 2847, 'growth': 15},
            {'name': 'Vue.js 技术报告', 'views': 1923, 'growth': 8}
        ],
        'recent_activities': [
            {
                'type': 'report',
                'description': '新增了 "Kubernetes 深度分析" 报告',
                'time': '2小时前'
            }
        ]
    }
    
    return render_template('new_frontend/components/admin-dashboard.html', 
                         stats=stats)

# ============================================================================
# API端点 (为前端提供数据)
# ============================================================================

@app.route('/api/search')
def api_search():
    """
    搜索API - 为前端搜索功能提供数据
    """
    query = request.args.get('q', '').strip()
    page = request.args.get('page', 1, type=int)
    
    if not query:
        return jsonify({'reports': [], 'total': 0})
    
    # reports, total = ResearchReport.search_reports(query, page, 10)
    
    # 模拟搜索结果
    reports = [
        {
            'id': 1,
            'name': 'React.js',
            'description': '用于构建用户界面的JavaScript库',
            'creator': '研究员A'
        }
    ]
    
    return jsonify({
        'reports': reports,
        'total': len(reports),
        'page': page
    })

@app.route('/api/analytics/<report_id>')
def api_analytics(report_id):
    """
    分析数据API - 为分析页面提供图表数据
    """
    # 获取真实的分析数据
    # analytics = get_report_analytics(report_id)
    
    # 模拟分析数据
    analytics = {
        'downloads': [12, 14, 16, 15, 17, 18.5],
        'versions': [
            {'version': 'v18.x', 'percentage': 65},
            {'version': 'v17.x', 'percentage': 20},
            {'version': 'v16.x', 'percentage': 12},
            {'version': '其他', 'percentage': 3}
        ],
        'performance': {
            'bundle_size': 85,
            'load_speed': 92,
            'memory_usage': 78
        }
    }
    
    return jsonify(analytics)

# ============================================================================
# 模板上下文处理器
# ============================================================================

@app.context_processor
def inject_template_vars():
    """
    为所有模板注入通用变量
    """
    return {
        'current_year': datetime.now().year,
        'app_name': '项目研究报告平台',
        'version': '2.0.0'
    }

# ============================================================================
# 错误处理
# ============================================================================

@app.errorhandler(404)
def not_found(error):
    """
    404错误页面 - 可以创建对应的错误页面模板
    """
    return render_template('new_frontend/components/error-404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    """
    500错误页面
    """
    return render_template('new_frontend/components/error-500.html'), 500

# ============================================================================
# 静态文件配置
# ============================================================================

@app.route('/assets/<path:filename>')
def serve_assets(filename):
    """
    提供新前端的静态资源文件
    """
    return app.send_static_file(f'new_frontend/assets/{filename}')

# ============================================================================
# 配置示例
# ============================================================================

if __name__ == '__main__':
    # 开发环境配置
    app.config.update(
        DEBUG=True,
        SECRET_KEY='your-secret-key-here',
        # 其他配置...
    )
    
    # 注册蓝图（如果使用蓝图架构）
    # from app.views.public import public_bp
    # from app.views.admin import admin_bp
    # app.register_blueprint(public_bp)
    # app.register_blueprint(admin_bp, url_prefix='/admin')
    
    app.run(host='0.0.0.0', port=5000)

# ============================================================================
# 集成步骤说明
# ============================================================================

"""
集成步骤：

1. 复制模板文件
   - 将 new_frontend/components/ 下的所有HTML文件复制到您的templates目录
   - 将 new_frontend/assets/ 下的CSS和JS文件复制到static目录

2. 更新路由
   - 修改现有路由以使用新的模板文件
   - 确保传递正确的数据到模板

3. 配置静态文件
   - 更新静态文件路径配置
   - 确保CSS和JS文件正确加载

4. 数据适配
   - 修改模板中的数据绑定以匹配您的数据结构
   - 更新API端点以提供前端所需的数据格式

5. 测试和调试
   - 测试所有页面的功能
   - 检查响应式设计在不同设备上的表现
   - 验证所有交互功能正常工作

6. 性能优化
   - 压缩CSS和JS文件
   - 配置CDN加速
   - 启用浏览器缓存
"""
