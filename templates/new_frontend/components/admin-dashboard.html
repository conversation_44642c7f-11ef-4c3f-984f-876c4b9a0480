<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理仪表板</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        .metric-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        .metric-card.blue {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        .metric-card.green {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
        .metric-card.purple {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }
        .sidebar {
            background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="flex h-screen" x-data="dashboardData()">
        <!-- 侧边栏 -->
        <div class="w-64 sidebar text-white flex-shrink-0">
            <div class="p-6">
                <div class="flex items-center space-x-3 mb-8">
                    <div class="w-10 h-10 bg-white bg-opacity-20 rounded-xl flex items-center justify-center">
                        <i class="fas fa-chart-line text-xl"></i>
                    </div>
                    <div>
                        <h1 class="font-bold text-lg">管理后台</h1>
                        <p class="text-white text-opacity-70 text-sm">研究报告平台</p>
                    </div>
                </div>

                <nav class="space-y-2">
                    <a href="#" :class="activeTab === 'dashboard' ? 'bg-white bg-opacity-20' : 'hover:bg-white hover:bg-opacity-10'" 
                       @click="activeTab = 'dashboard'"
                       class="flex items-center space-x-3 p-3 rounded-xl transition-all">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>仪表板</span>
                    </a>
                    <a href="#" :class="activeTab === 'reports' ? 'bg-white bg-opacity-20' : 'hover:bg-white hover:bg-opacity-10'" 
                       @click="activeTab = 'reports'"
                       class="flex items-center space-x-3 p-3 rounded-xl transition-all">
                        <i class="fas fa-file-alt"></i>
                        <span>报告管理</span>
                    </a>
                    <a href="#" :class="activeTab === 'requests' ? 'bg-white bg-opacity-20' : 'hover:bg-white hover:bg-opacity-10'" 
                       @click="activeTab = 'requests'"
                       class="flex items-center space-x-3 p-3 rounded-xl transition-all">
                        <i class="fas fa-inbox"></i>
                        <span>用户请求</span>
                        <span class="bg-red-500 text-white text-xs px-2 py-1 rounded-full">3</span>
                    </a>
                    <a href="#" class="flex items-center space-x-3 p-3 rounded-xl hover:bg-white hover:bg-opacity-10 transition-all">
                        <i class="fas fa-users"></i>
                        <span>用户管理</span>
                    </a>
                    <a href="#" class="flex items-center space-x-3 p-3 rounded-xl hover:bg-white hover:bg-opacity-10 transition-all">
                        <i class="fas fa-cog"></i>
                        <span>系统设置</span>
                    </a>
                </nav>
            </div>

            <div class="absolute bottom-6 left-6 right-6">
                <div class="bg-white bg-opacity-10 rounded-xl p-4">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                            <i class="fas fa-user"></i>
                        </div>
                        <div>
                            <p class="font-semibold">管理员</p>
                            <p class="text-white text-opacity-70 text-sm"><EMAIL></p>
                        </div>
                    </div>
                    <button class="w-full mt-3 bg-white bg-opacity-20 text-white py-2 px-4 rounded-lg text-sm hover:bg-opacity-30 transition-all">
                        <i class="fas fa-sign-out-alt mr-2"></i>
                        退出登录
                    </button>
                </div>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="flex-1 overflow-auto">
            <!-- 顶部栏 -->
            <div class="bg-white border-b border-gray-200 p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h2 class="text-2xl font-bold text-gray-800">仪表板概览</h2>
                        <p class="text-gray-600">欢迎回来，这里是您的管理中心</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <button class="p-2 text-gray-400 hover:text-gray-600 transition-colors">
                            <i class="fas fa-bell"></i>
                        </button>
                        <button class="p-2 text-gray-400 hover:text-gray-600 transition-colors">
                            <i class="fas fa-search"></i>
                        </button>
                        <button class="bg-blue-600 text-white px-4 py-2 rounded-xl hover:bg-blue-700 transition-colors">
                            <i class="fas fa-plus mr-2"></i>
                            新建报告
                        </button>
                    </div>
                </div>
            </div>

            <!-- 仪表板内容 -->
            <div class="p-6">
                <!-- 统计卡片 -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="metric-card blue text-white p-6 rounded-2xl card-hover">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-white text-opacity-80 text-sm">总报告数</p>
                                <p class="text-3xl font-bold" x-text="stats.totalReports"></p>
                                <p class="text-white text-opacity-80 text-sm mt-1">
                                    <i class="fas fa-arrow-up mr-1"></i>
                                    +12% 本月
                                </p>
                            </div>
                            <div class="bg-white bg-opacity-20 p-3 rounded-xl">
                                <i class="fas fa-file-alt text-2xl"></i>
                            </div>
                        </div>
                    </div>

                    <div class="metric-card green text-white p-6 rounded-2xl card-hover">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-white text-opacity-80 text-sm">待处理请求</p>
                                <p class="text-3xl font-bold" x-text="stats.pendingRequests"></p>
                                <p class="text-white text-opacity-80 text-sm mt-1">
                                    <i class="fas fa-clock mr-1"></i>
                                    需要处理
                                </p>
                            </div>
                            <div class="bg-white bg-opacity-20 p-3 rounded-xl">
                                <i class="fas fa-inbox text-2xl"></i>
                            </div>
                        </div>
                    </div>

                    <div class="metric-card purple text-white p-6 rounded-2xl card-hover">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-white text-opacity-80 text-sm">总浏览量</p>
                                <p class="text-3xl font-bold" x-text="stats.totalViews"></p>
                                <p class="text-white text-opacity-80 text-sm mt-1">
                                    <i class="fas fa-arrow-up mr-1"></i>
                                    +8.5% 本周
                                </p>
                            </div>
                            <div class="bg-white bg-opacity-20 p-3 rounded-xl">
                                <i class="fas fa-eye text-2xl"></i>
                            </div>
                        </div>
                    </div>

                    <div class="metric-card text-white p-6 rounded-2xl card-hover">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-white text-opacity-80 text-sm">活跃用户</p>
                                <p class="text-3xl font-bold" x-text="stats.activeUsers"></p>
                                <p class="text-white text-opacity-80 text-sm mt-1">
                                    <i class="fas fa-arrow-up mr-1"></i>
                                    +15% 本月
                                </p>
                            </div>
                            <div class="bg-white bg-opacity-20 p-3 rounded-xl">
                                <i class="fas fa-users text-2xl"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 图表和数据 -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                    <!-- 访问趋势图 -->
                    <div class="bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="text-lg font-bold text-gray-800">
                                <i class="fas fa-chart-line text-blue-600 mr-2"></i>
                                访问趋势
                            </h3>
                            <select class="border border-gray-200 rounded-lg px-3 py-1 text-sm">
                                <option>最近7天</option>
                                <option>最近30天</option>
                                <option>最近90天</option>
                            </select>
                        </div>
                        <canvas id="visitsChart" class="w-full h-64"></canvas>
                    </div>

                    <!-- 热门报告 -->
                    <div class="bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
                        <h3 class="text-lg font-bold text-gray-800 mb-6">
                            <i class="fas fa-fire text-orange-600 mr-2"></i>
                            热门报告
                        </h3>
                        <div class="space-y-4">
                            <template x-for="(report, index) in popularReports" :key="report.id">
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                            <span class="text-blue-600 font-semibold text-sm" x-text="index + 1"></span>
                                        </div>
                                        <div>
                                            <h4 class="font-semibold text-gray-800" x-text="report.name"></h4>
                                            <p class="text-sm text-gray-600" x-text="report.views + ' 次浏览'"></p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <p class="text-sm font-semibold text-green-600" x-text="'+' + report.growth + '%'"></p>
                                        <p class="text-xs text-gray-500">本周增长</p>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>

                <!-- 最近活动和待办事项 -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- 最近活动 -->
                    <div class="bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
                        <h3 class="text-lg font-bold text-gray-800 mb-6">
                            <i class="fas fa-clock text-purple-600 mr-2"></i>
                            最近活动
                        </h3>
                        <div class="space-y-4">
                            <template x-for="activity in recentActivities" :key="activity.id">
                                <div class="flex items-start space-x-3">
                                    <div :class="activity.type === 'report' ? 'bg-blue-100 text-blue-600' : activity.type === 'request' ? 'bg-green-100 text-green-600' : 'bg-purple-100 text-purple-600'" 
                                         class="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                                        <i :class="activity.icon" class="text-sm"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-gray-800" x-text="activity.description"></p>
                                        <p class="text-sm text-gray-500" x-text="activity.time"></p>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>

                    <!-- 待办事项 -->
                    <div class="bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
                        <h3 class="text-lg font-bold text-gray-800 mb-6">
                            <i class="fas fa-tasks text-red-600 mr-2"></i>
                            待办事项
                        </h3>
                        <div class="space-y-3">
                            <template x-for="todo in todoList" :key="todo.id">
                                <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-xl">
                                    <input type="checkbox" 
                                           :checked="todo.completed"
                                           @change="toggleTodo(todo.id)"
                                           class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                    <div class="flex-1">
                                        <p :class="todo.completed ? 'line-through text-gray-500' : 'text-gray-800'" 
                                           x-text="todo.task"></p>
                                        <p class="text-sm text-gray-500" x-text="todo.deadline"></p>
                                    </div>
                                    <div :class="todo.priority === 'high' ? 'bg-red-100 text-red-600' : todo.priority === 'medium' ? 'bg-yellow-100 text-yellow-600' : 'bg-green-100 text-green-600'" 
                                         class="px-2 py-1 rounded-full text-xs font-medium">
                                        <span x-text="todo.priority === 'high' ? '高' : todo.priority === 'medium' ? '中' : '低'"></span>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function dashboardData() {
            return {
                activeTab: 'dashboard',
                stats: {
                    totalReports: 24,
                    pendingRequests: 3,
                    totalViews: '12.5K',
                    activeUsers: 1247
                },
                popularReports: [
                    { id: 1, name: 'React.js 深度分析', views: 2847, growth: 15 },
                    { id: 2, name: 'Vue.js 技术报告', views: 1923, growth: 8 },
                    { id: 3, name: 'Node.js 生态研究', views: 1654, growth: 12 },
                    { id: 4, name: 'Docker 容器技术', views: 1432, growth: 5 }
                ],
                recentActivities: [
                    { id: 1, type: 'report', icon: 'fas fa-file-alt', description: '新增了 "Kubernetes 深度分析" 报告', time: '2小时前' },
                    { id: 2, type: 'request', icon: 'fas fa-inbox', description: '收到新的项目申请：Angular 技术研究', time: '4小时前' },
                    { id: 3, type: 'user', icon: 'fas fa-user', description: '用户 <EMAIL> 查看了 React.js 报告', time: '6小时前' },
                    { id: 4, type: 'report', icon: 'fas fa-edit', description: '更新了 "Vue.js 技术报告" 的内容', time: '1天前' }
                ],
                todoList: [
                    { id: 1, task: '审核 Angular 项目申请', deadline: '今天截止', priority: 'high', completed: false },
                    { id: 2, task: '更新 React.js 报告数据', deadline: '明天截止', priority: 'medium', completed: false },
                    { id: 3, task: '回复用户邮件咨询', deadline: '本周内', priority: 'low', completed: true },
                    { id: 4, task: '准备月度统计报告', deadline: '下周一', priority: 'medium', completed: false }
                ],
                
                init() {
                    this.initChart();
                },
                
                initChart() {
                    const ctx = document.getElementById('visitsChart').getContext('2d');
                    new Chart(ctx, {
                        type: 'line',
                        data: {
                            labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
                            datasets: [{
                                label: '访问量',
                                data: [120, 190, 300, 500, 200, 300, 450],
                                borderColor: 'rgb(102, 126, 234)',
                                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                                tension: 0.4,
                                fill: true
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    display: false
                                }
                            },
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    grid: {
                                        color: 'rgba(0, 0, 0, 0.05)'
                                    }
                                },
                                x: {
                                    grid: {
                                        display: false
                                    }
                                }
                            }
                        }
                    });
                },
                
                toggleTodo(id) {
                    const todo = this.todoList.find(t => t.id === id);
                    if (todo) {
                        todo.completed = !todo.completed;
                    }
                }
            }
        }
    </script>
</body>
</html>
