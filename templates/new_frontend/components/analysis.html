<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据分析 - React.js 项目分析</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        .metric-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        .metric-card.blue {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        .metric-card.green {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
        .metric-card.purple {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="max-w-7xl mx-auto p-6" x-data="analysisData()">
        <!-- 页面头部 -->
        <div class="mb-8">
            <div class="flex items-center justify-between mb-6">
                <div>
                    <h1 class="text-3xl font-bold text-gray-800 mb-2">
                        <i class="fas fa-chart-bar text-blue-600 mr-3"></i>
                        <span x-text="projectName"></span> 交互式分析页面
                    </h1>
                    <p class="text-gray-600">项目的交互式数据分析和可视化展示</p>
                </div>
                <div class="flex space-x-3">
                    <button @click="goBack()" class="bg-white border border-gray-200 text-gray-600 px-4 py-2 rounded-xl hover:bg-gray-50 transition-colors">
                        <i class="fas fa-arrow-left mr-2"></i>
                        返回列表
                    </button>
                    <button class="bg-white border border-gray-200 text-gray-600 px-4 py-2 rounded-xl hover:bg-gray-50 transition-colors">
                        <i class="fas fa-download mr-2"></i>
                        导出分析
                    </button>
                    <button class="gradient-bg text-white px-4 py-2 rounded-xl hover:opacity-90 transition-opacity">
                        <i class="fas fa-share mr-2"></i>
                        分享页面
                    </button>
                </div>
            </div>

            <!-- 加载状态 -->
            <div x-show="loading" class="text-center py-12">
                <div class="loading-spinner mx-auto mb-4"></div>
                <p class="text-gray-600">正在加载交互式分析页面...</p>
            </div>
        </div>

        <!-- HTML内容渲染区域 -->
        <div x-show="!loading" class="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
            <!-- 内容头部 -->
            <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-800">
                        <i class="fas fa-code mr-2"></i>
                        交互式分析内容
                    </h3>
                    <div class="flex items-center space-x-2 text-sm text-gray-500">
                        <i class="fas fa-clock mr-1"></i>
                        <span>最后更新：<span x-text="lastUpdated"></span></span>
                    </div>
                </div>
            </div>

            <!-- HTML内容iframe -->
            <div class="relative">
                <iframe x-ref="analysisFrame"
                        :src="htmlFilePath"
                        class="w-full border-0"
                        style="min-height: 800px;"
                        @load="onFrameLoad()"
                        sandbox="allow-scripts allow-same-origin allow-forms">
                </iframe>

                <!-- 错误状态 -->
                <div x-show="error" class="absolute inset-0 flex items-center justify-center bg-gray-50">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">加载失败</h3>
                        <p class="text-gray-600 mb-4">无法加载交互式分析页面</p>
                        <button @click="retryLoad()" class="btn-gradient text-white px-4 py-2 rounded-xl font-medium">
                            <i class="fas fa-redo mr-2"></i>
                            重新加载
                        </button>
                    </div>
                </div>
            </div>
        </div>




    </div>

    <script>
        function analysisData() {
            return {
                loading: true,
                error: false,
                projectName: 'React.js',
                lastUpdated: '2024-01-20',
                htmlFilePath: '',

                init() {
                    this.loadAnalysisData();
                },

                async loadAnalysisData() {
                    try {
                        // 从URL参数获取报告ID
                        const urlParams = new URLSearchParams(window.location.search);
                        const reportId = urlParams.get('id') || '1';

                        // 模拟API调用获取报告信息
                        await new Promise(resolve => setTimeout(resolve, 1000));

                        // 这里应该调用实际的API获取HTML文件路径
                        // const response = await fetch(`/api/report/${reportId}/analysis`);
                        // const data = await response.json();

                        // 模拟数据
                        const mockData = {
                            1: { name: 'React.js', htmlPath: 'demo-analysis-react.html' },
                            2: { name: 'Vue.js', htmlPath: 'demo-analysis-vue.html' },
                            3: { name: 'Node.js', htmlPath: 'demo-analysis-node.html' }
                        };

                        const reportData = mockData[reportId] || mockData['1'];
                        this.projectName = reportData.name;
                        this.htmlFilePath = reportData.htmlPath;

                        this.loading = false;

                    } catch (error) {
                        console.error('加载分析数据失败:', error);
                        this.error = true;
                        this.loading = false;
                    }
                },

                onFrameLoad() {
                    // iframe加载完成后的处理
                    console.log('分析页面加载完成');

                    // 可以在这里添加与iframe的通信逻辑
                    const frame = this.$refs.analysisFrame;
                    if (frame) {
                        // 自动调整iframe高度
                        try {
                            const frameDoc = frame.contentDocument || frame.contentWindow.document;
                            const height = frameDoc.body.scrollHeight;
                            frame.style.height = Math.max(height, 800) + 'px';
                        } catch (e) {
                            // 跨域限制，使用默认高度
                            console.log('无法访问iframe内容，使用默认高度');
                        }
                    }
                },

                retryLoad() {
                    this.error = false;
                    this.loading = true;
                    this.loadAnalysisData();
                },

                goBack() {
                    // 返回报告列表
                    window.history.back();
                }
            }
        }
    </script>
</body>
</html>
