<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>申请新项目研究</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .btn-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: all 0.3s ease;
        }
        .btn-gradient:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        .form-focus {
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        .step-active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .step-completed {
            background: #10b981;
            color: white;
        }
        .step-inactive {
            background: #f3f4f6;
            color: #6b7280;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="max-w-4xl mx-auto p-6" x-data="requestData()">
        <!-- 页面头部 -->
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-800 mb-2">
                <i class="fas fa-plus-circle text-blue-600 mr-3"></i>
                申请新项目研究
            </h1>
            <p class="text-gray-600">提交您希望我们研究分析的项目，我们将为您提供专业的技术报告</p>
        </div>

        <!-- 步骤指示器 -->
        <div class="flex items-center justify-center mb-8">
            <div class="flex items-center space-x-4">
                <div class="flex items-center">
                    <div :class="currentStep >= 1 ? 'step-active' : 'step-inactive'" 
                         class="w-10 h-10 rounded-full flex items-center justify-center font-semibold">
                        <i class="fas fa-info-circle" x-show="currentStep >= 1"></i>
                        <span x-show="currentStep < 1">1</span>
                    </div>
                    <span class="ml-2 text-sm font-medium text-gray-600">项目信息</span>
                </div>
                <div class="w-16 h-1 bg-gray-200 rounded">
                    <div :class="currentStep >= 2 ? 'bg-blue-600' : 'bg-gray-200'" 
                         class="h-full rounded transition-all duration-300" 
                         :style="currentStep >= 2 ? 'width: 100%' : 'width: 0%'"></div>
                </div>
                <div class="flex items-center">
                    <div :class="currentStep >= 2 ? 'step-active' : 'step-inactive'" 
                         class="w-10 h-10 rounded-full flex items-center justify-center font-semibold">
                        <i class="fas fa-user" x-show="currentStep >= 2"></i>
                        <span x-show="currentStep < 2">2</span>
                    </div>
                    <span class="ml-2 text-sm font-medium text-gray-600">联系信息</span>
                </div>
                <div class="w-16 h-1 bg-gray-200 rounded">
                    <div :class="currentStep >= 3 ? 'bg-blue-600' : 'bg-gray-200'" 
                         class="h-full rounded transition-all duration-300" 
                         :style="currentStep >= 3 ? 'width: 100%' : 'width: 0%'"></div>
                </div>
                <div class="flex items-center">
                    <div :class="currentStep >= 3 ? 'step-active' : 'step-inactive'" 
                         class="w-10 h-10 rounded-full flex items-center justify-center font-semibold">
                        <i class="fas fa-check" x-show="currentStep >= 3"></i>
                        <span x-show="currentStep < 3">3</span>
                    </div>
                    <span class="ml-2 text-sm font-medium text-gray-600">确认提交</span>
                </div>
            </div>
        </div>

        <!-- 表单内容 -->
        <div class="bg-white rounded-2xl shadow-sm border border-gray-100 p-8">
            <!-- 步骤1：项目信息 -->
            <div x-show="currentStep === 1" x-transition class="space-y-6">
                <h3 class="text-xl font-bold text-gray-800 mb-6">
                    <i class="fas fa-project-diagram text-blue-600 mr-2"></i>
                    项目基本信息
                </h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-semibold text-gray-700 mb-2">
                            项目名称 <span class="text-red-500">*</span>
                        </label>
                        <input type="text" 
                               x-model="formData.projectName"
                               placeholder="例如：React.js"
                               class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-semibold text-gray-700 mb-2">
                            官方网站 <span class="text-red-500">*</span>
                        </label>
                        <input type="url" 
                               x-model="formData.website"
                               placeholder="https://example.com"
                               class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all">
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-semibold text-gray-700 mb-2">
                        项目类型
                    </label>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                        <label class="flex items-center p-3 border border-gray-200 rounded-xl cursor-pointer hover:bg-gray-50 transition-colors">
                            <input type="radio" x-model="formData.category" value="frontend" class="mr-2">
                            <i class="fas fa-desktop text-blue-600 mr-2"></i>
                            <span class="text-sm">前端框架</span>
                        </label>
                        <label class="flex items-center p-3 border border-gray-200 rounded-xl cursor-pointer hover:bg-gray-50 transition-colors">
                            <input type="radio" x-model="formData.category" value="backend" class="mr-2">
                            <i class="fas fa-server text-green-600 mr-2"></i>
                            <span class="text-sm">后端技术</span>
                        </label>
                        <label class="flex items-center p-3 border border-gray-200 rounded-xl cursor-pointer hover:bg-gray-50 transition-colors">
                            <input type="radio" x-model="formData.category" value="database" class="mr-2">
                            <i class="fas fa-database text-purple-600 mr-2"></i>
                            <span class="text-sm">数据库</span>
                        </label>
                        <label class="flex items-center p-3 border border-gray-200 rounded-xl cursor-pointer hover:bg-gray-50 transition-colors">
                            <input type="radio" x-model="formData.category" value="other" class="mr-2">
                            <i class="fas fa-cog text-gray-600 mr-2"></i>
                            <span class="text-sm">其他</span>
                        </label>
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-semibold text-gray-700 mb-2">
                        研究重点 <span class="text-gray-500">(可多选)</span>
                    </label>
                    <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
                        <label class="flex items-center p-3 border border-gray-200 rounded-xl cursor-pointer hover:bg-gray-50 transition-colors">
                            <input type="checkbox" x-model="formData.focus" value="performance" class="mr-2">
                            <span class="text-sm">性能分析</span>
                        </label>
                        <label class="flex items-center p-3 border border-gray-200 rounded-xl cursor-pointer hover:bg-gray-50 transition-colors">
                            <input type="checkbox" x-model="formData.focus" value="security" class="mr-2">
                            <span class="text-sm">安全性</span>
                        </label>
                        <label class="flex items-center p-3 border border-gray-200 rounded-xl cursor-pointer hover:bg-gray-50 transition-colors">
                            <input type="checkbox" x-model="formData.focus" value="ecosystem" class="mr-2">
                            <span class="text-sm">生态系统</span>
                        </label>
                        <label class="flex items-center p-3 border border-gray-200 rounded-xl cursor-pointer hover:bg-gray-50 transition-colors">
                            <input type="checkbox" x-model="formData.focus" value="comparison" class="mr-2">
                            <span class="text-sm">竞品对比</span>
                        </label>
                        <label class="flex items-center p-3 border border-gray-200 rounded-xl cursor-pointer hover:bg-gray-50 transition-colors">
                            <input type="checkbox" x-model="formData.focus" value="trends" class="mr-2">
                            <span class="text-sm">发展趋势</span>
                        </label>
                        <label class="flex items-center p-3 border border-gray-200 rounded-xl cursor-pointer hover:bg-gray-50 transition-colors">
                            <input type="checkbox" x-model="formData.focus" value="community" class="mr-2">
                            <span class="text-sm">社区活跃度</span>
                        </label>
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-semibold text-gray-700 mb-2">
                        详细说明
                    </label>
                    <textarea x-model="formData.description"
                              placeholder="请详细描述您希望我们重点关注的方面，或者您对这个项目的具体疑问..."
                              rows="4"
                              class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all resize-none"></textarea>
                </div>
            </div>

            <!-- 步骤2：联系信息 -->
            <div x-show="currentStep === 2" x-transition class="space-y-6">
                <h3 class="text-xl font-bold text-gray-800 mb-6">
                    <i class="fas fa-user text-blue-600 mr-2"></i>
                    联系信息
                </h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-semibold text-gray-700 mb-2">
                            您的姓名 <span class="text-red-500">*</span>
                        </label>
                        <input type="text" 
                               x-model="formData.userName"
                               placeholder="请输入您的姓名"
                               class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-semibold text-gray-700 mb-2">
                            邮箱地址 <span class="text-red-500">*</span>
                        </label>
                        <input type="email" 
                               x-model="formData.email"
                               placeholder="<EMAIL>"
                               class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-semibold text-gray-700 mb-2">
                            公司/组织
                        </label>
                        <input type="text" 
                               x-model="formData.company"
                               placeholder="可选填"
                               class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-semibold text-gray-700 mb-2">
                            职位/角色
                        </label>
                        <select x-model="formData.role" 
                                class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all">
                            <option value="">请选择</option>
                            <option value="developer">开发工程师</option>
                            <option value="architect">架构师</option>
                            <option value="manager">技术经理</option>
                            <option value="student">学生</option>
                            <option value="researcher">研究员</option>
                            <option value="other">其他</option>
                        </select>
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-semibold text-gray-700 mb-2">
                        使用场景
                    </label>
                    <textarea x-model="formData.useCase"
                              placeholder="请简单描述您计划如何使用这个项目，或者您的应用场景..."
                              rows="3"
                              class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all resize-none"></textarea>
                </div>

                <div>
                    <label class="block text-sm font-semibold text-gray-700 mb-2">
                        期望完成时间
                    </label>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                        <label class="flex items-center p-3 border border-gray-200 rounded-xl cursor-pointer hover:bg-gray-50 transition-colors">
                            <input type="radio" x-model="formData.timeline" value="urgent" class="mr-2">
                            <span class="text-sm">1周内（加急）</span>
                        </label>
                        <label class="flex items-center p-3 border border-gray-200 rounded-xl cursor-pointer hover:bg-gray-50 transition-colors">
                            <input type="radio" x-model="formData.timeline" value="normal" class="mr-2">
                            <span class="text-sm">2-3周（正常）</span>
                        </label>
                        <label class="flex items-center p-3 border border-gray-200 rounded-xl cursor-pointer hover:bg-gray-50 transition-colors">
                            <input type="radio" x-model="formData.timeline" value="flexible" class="mr-2">
                            <span class="text-sm">时间灵活</span>
                        </label>
                    </div>
                </div>
            </div>

            <!-- 步骤3：确认提交 -->
            <div x-show="currentStep === 3" x-transition class="space-y-6">
                <h3 class="text-xl font-bold text-gray-800 mb-6">
                    <i class="fas fa-check-circle text-blue-600 mr-2"></i>
                    确认信息
                </h3>
                
                <div class="bg-gray-50 rounded-xl p-6 space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <span class="text-sm font-semibold text-gray-600">项目名称：</span>
                            <span class="text-gray-800" x-text="formData.projectName || '未填写'"></span>
                        </div>
                        <div>
                            <span class="text-sm font-semibold text-gray-600">官方网站：</span>
                            <span class="text-gray-800" x-text="formData.website || '未填写'"></span>
                        </div>
                        <div>
                            <span class="text-sm font-semibold text-gray-600">项目类型：</span>
                            <span class="text-gray-800" x-text="getCategoryName(formData.category)"></span>
                        </div>
                        <div>
                            <span class="text-sm font-semibold text-gray-600">联系邮箱：</span>
                            <span class="text-gray-800" x-text="formData.email || '未填写'"></span>
                        </div>
                    </div>
                    
                    <div>
                        <span class="text-sm font-semibold text-gray-600">研究重点：</span>
                        <span class="text-gray-800" x-text="getFocusNames(formData.focus)"></span>
                    </div>
                </div>

                <div class="bg-blue-50 border border-blue-200 rounded-xl p-4">
                    <div class="flex items-start">
                        <i class="fas fa-info-circle text-blue-600 mt-1 mr-3"></i>
                        <div>
                            <h4 class="font-semibold text-blue-800 mb-2">提交须知</h4>
                            <ul class="text-sm text-blue-700 space-y-1">
                                <li>• 我们将在1-2个工作日内审核您的申请</li>
                                <li>• 审核通过后，我们会通过邮件与您联系确认详细需求</li>
                                <li>• 研究报告将在约定时间内完成并发送到您的邮箱</li>
                                <li>• 如有疑问，请联系我们：<EMAIL></li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="flex items-center">
                    <input type="checkbox" x-model="formData.agreed" class="mr-3">
                    <label class="text-sm text-gray-600">
                        我已阅读并同意 <a href="#" class="text-blue-600 hover:underline">服务条款</a> 和 <a href="#" class="text-blue-600 hover:underline">隐私政策</a>
                    </label>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="flex justify-between mt-8 pt-6 border-t border-gray-200">
                <button x-show="currentStep > 1" 
                        @click="currentStep--"
                        class="px-6 py-3 border border-gray-200 text-gray-600 rounded-xl hover:bg-gray-50 transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>
                    上一步
                </button>
                <div x-show="currentStep === 1"></div>
                
                <button x-show="currentStep < 3" 
                        @click="nextStep()"
                        class="btn-gradient text-white px-6 py-3 rounded-xl font-medium">
                    下一步
                    <i class="fas fa-arrow-right ml-2"></i>
                </button>
                
                <button x-show="currentStep === 3" 
                        @click="submitForm()"
                        :disabled="!formData.agreed"
                        :class="formData.agreed ? 'btn-gradient text-white' : 'bg-gray-300 text-gray-500 cursor-not-allowed'"
                        class="px-6 py-3 rounded-xl font-medium transition-all">
                    <i class="fas fa-paper-plane mr-2"></i>
                    提交申请
                </button>
            </div>
        </div>

        <!-- 成功提示 -->
        <div x-show="submitted" x-transition class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white rounded-2xl p-8 max-w-md mx-4 text-center">
                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-check text-green-600 text-2xl"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-800 mb-2">申请提交成功！</h3>
                <p class="text-gray-600 mb-6">我们已收到您的申请，将在1-2个工作日内与您联系。</p>
                <button @click="resetForm()" 
                        class="btn-gradient text-white px-6 py-3 rounded-xl font-medium">
                    提交新申请
                </button>
            </div>
        </div>
    </div>

    <script>
        function requestData() {
            return {
                currentStep: 1,
                submitted: false,
                formData: {
                    projectName: '',
                    website: '',
                    category: '',
                    focus: [],
                    description: '',
                    userName: '',
                    email: '',
                    company: '',
                    role: '',
                    useCase: '',
                    timeline: '',
                    agreed: false
                },
                
                nextStep() {
                    if (this.validateCurrentStep()) {
                        this.currentStep++;
                    }
                },
                
                validateCurrentStep() {
                    if (this.currentStep === 1) {
                        return this.formData.projectName && this.formData.website;
                    } else if (this.currentStep === 2) {
                        return this.formData.userName && this.formData.email;
                    }
                    return true;
                },
                
                submitForm() {
                    if (this.formData.agreed) {
                        // 这里可以添加实际的提交逻辑
                        this.submitted = true;
                    }
                },
                
                resetForm() {
                    this.currentStep = 1;
                    this.submitted = false;
                    this.formData = {
                        projectName: '',
                        website: '',
                        category: '',
                        focus: [],
                        description: '',
                        userName: '',
                        email: '',
                        company: '',
                        role: '',
                        useCase: '',
                        timeline: '',
                        agreed: false
                    };
                },
                
                getCategoryName(category) {
                    const categories = {
                        'frontend': '前端框架',
                        'backend': '后端技术',
                        'database': '数据库',
                        'other': '其他'
                    };
                    return categories[category] || '未选择';
                },
                
                getFocusNames(focus) {
                    const focusMap = {
                        'performance': '性能分析',
                        'security': '安全性',
                        'ecosystem': '生态系统',
                        'comparison': '竞品对比',
                        'trends': '发展趋势',
                        'community': '社区活跃度'
                    };
                    return focus.map(f => focusMap[f]).join('、') || '未选择';
                }
            }
        }
    </script>
</body>
</html>
