<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>报告详情 - React.js 深度分析</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .text-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .btn-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: all 0.3s ease;
        }
        .btn-gradient:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        .markdown-content {
            line-height: 1.7;
        }
        .markdown-content h1 { @apply text-2xl font-bold text-gray-800 mb-4 mt-6; }
        .markdown-content h2 { @apply text-xl font-bold text-gray-800 mb-3 mt-5; }
        .markdown-content h3 { @apply text-lg font-semibold text-gray-700 mb-2 mt-4; }
        .markdown-content p { @apply text-gray-600 mb-4; }
        .markdown-content ul { @apply list-disc list-inside text-gray-600 mb-4 space-y-1; }
        .markdown-content ol { @apply list-decimal list-inside text-gray-600 mb-4 space-y-1; }
        .markdown-content code { @apply bg-gray-100 px-2 py-1 rounded text-sm font-mono; }
        .markdown-content pre { @apply bg-gray-100 p-4 rounded-lg overflow-x-auto mb-4; }
        .markdown-content blockquote { @apply border-l-4 border-blue-500 pl-4 italic text-gray-600 mb-4; }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="max-w-4xl mx-auto p-6" x-data="reportData()">
        <!-- 返回按钮 -->
        <div class="mb-6">
            <button @click="goBack()" class="flex items-center text-gray-600 hover:text-gray-800 transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>
                返回报告列表
            </button>
        </div>

        <!-- 加载状态 -->
        <div x-show="loading" class="text-center py-12">
            <div class="loading-spinner mx-auto mb-4"></div>
            <p class="text-gray-600">正在加载调研报告...</p>
        </div>

        <!-- 报告头部 -->
        <div x-show="!loading && !error" class="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden mb-6">
            <!-- 项目封面 -->
            <div class="h-48 bg-gradient-to-br from-blue-400 to-purple-500 relative">
                <div class="absolute inset-0 bg-black bg-opacity-30 flex items-end">
                    <div class="p-8 text-white">
                        <h1 class="text-3xl font-bold mb-2" x-text="projectName + ' 调研报告'"></h1>
                        <p class="text-lg opacity-90">深度技术分析与评估报告</p>
                    </div>
                </div>
            </div>

            <!-- 项目信息 -->
            <div class="p-6">
                <div class="flex flex-wrap items-center justify-between">
                    <div class="flex items-center space-x-4 mb-4 md:mb-0">
                        <div class="flex items-center text-sm text-gray-600">
                            <i class="fas fa-user mr-2"></i>
                            <span>研究员：<span x-text="creator"></span></span>
                        </div>
                        <div class="flex items-center text-sm text-gray-600">
                            <i class="fas fa-calendar mr-2"></i>
                            <span>创建时间：<span x-text="createdAt"></span></span>
                        </div>
                        <div class="flex items-center text-sm text-gray-600">
                            <i class="fas fa-clock mr-2"></i>
                            <span>更新时间：<span x-text="updatedAt"></span></span>
                        </div>
                    </div>
                    <div class="flex space-x-2">
                        <button @click="viewAnalysis()" class="btn-gradient text-white px-4 py-2 rounded-xl font-medium">
                            <i class="fas fa-chart-bar mr-2"></i>
                            查看分析页面
                        </button>
                        <button class="bg-gray-100 text-gray-600 px-4 py-2 rounded-xl font-medium hover:bg-gray-200 transition-colors">
                            <i class="fas fa-download mr-2"></i>
                            下载报告
                        </button>
                        <button class="bg-gray-100 text-gray-600 px-4 py-2 rounded-xl font-medium hover:bg-gray-200 transition-colors">
                            <i class="fas fa-share mr-2"></i>
                            分享
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 报告内容 -->
        <div x-show="!loading && !error" class="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
            <!-- 内容头部 -->
            <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-800">
                        <i class="fas fa-file-alt mr-2"></i>
                        调研报告内容
                    </h3>
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center space-x-2 text-sm text-gray-500">
                            <i class="fas fa-file-code mr-1"></i>
                            <span>Markdown 格式</span>
                        </div>
                        <div class="flex items-center space-x-2 text-sm text-gray-500">
                            <i class="fas fa-clock mr-1"></i>
                            <span>最后更新：<span x-text="updatedAt"></span></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Markdown内容渲染区域 -->
            <div class="p-8">
                <div x-show="markdownLoading" class="text-center py-12">
                    <div class="loading-spinner mx-auto mb-4"></div>
                    <p class="text-gray-600">正在加载报告内容...</p>
                </div>

                <div x-show="!markdownLoading" class="markdown-content" x-html="markdownContent">
                    <!-- Markdown内容将在这里渲染 -->
                </div>
            </div>
        </div>

        <!-- 错误状态 -->
        <div x-show="error" class="bg-white rounded-2xl shadow-sm border border-gray-100 p-8 text-center">
            <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">加载失败</h3>
            <p class="text-gray-600 mb-4">无法加载调研报告内容</p>
            <button @click="retryLoad()" class="btn-gradient text-white px-4 py-2 rounded-xl font-medium">
                <i class="fas fa-redo mr-2"></i>
                重新加载
            </button>
        </div>

    </div>

    <!-- 引入Markdown解析库 -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>

    <script>
        function reportData() {
            return {
                loading: true,
                markdownLoading: false,
                error: false,
                projectName: 'React.js',
                creator: '张三',
                createdAt: '2024-01-15',
                updatedAt: '2024-01-20',
                markdownContent: '',
                reportId: null,

                init() {
                    this.loadReportData();
                },

                async loadReportData() {
                    try {
                        // 从URL参数获取报告ID
                        const urlParams = new URLSearchParams(window.location.search);
                        this.reportId = urlParams.get('id') || '1';

                        // 模拟API调用获取报告基本信息
                        await new Promise(resolve => setTimeout(resolve, 800));

                        // 这里应该调用实际的API获取报告信息
                        // const response = await fetch(`/api/report/${this.reportId}`);
                        // const data = await response.json();

                        // 模拟数据
                        const mockData = {
                            1: { name: 'React.js', creator: '张三', created_at: '2024-01-15', updated_at: '2024-01-20' },
                            2: { name: 'Vue.js', creator: '李四', created_at: '2024-01-10', updated_at: '2024-01-18' },
                            3: { name: 'Node.js', creator: '王五', created_at: '2024-01-08', updated_at: '2024-01-16' }
                        };

                        const reportData = mockData[this.reportId] || mockData['1'];
                        this.projectName = reportData.name;
                        this.creator = reportData.creator;
                        this.createdAt = reportData.created_at;
                        this.updatedAt = reportData.updated_at;

                        this.loading = false;

                        // 加载Markdown内容
                        await this.loadMarkdownContent();

                    } catch (error) {
                        console.error('加载报告数据失败:', error);
                        this.error = true;
                        this.loading = false;
                    }
                },

                async loadMarkdownContent() {
                    this.markdownLoading = true;

                    try {
                        // 这里应该调用实际的API获取Markdown文件内容
                        // const response = await fetch(`/api/report/${this.reportId}/markdown`);
                        // const markdownText = await response.text();

                        // 模拟Markdown内容
                        const mockMarkdown = `# ${this.projectName} 深度技术分析报告

## 1. 项目概述

${this.projectName} 是一个现代化的技术项目，具有以下特点：

- **高性能**：优化的架构设计确保了出色的性能表现
- **易用性**：简洁的API设计，降低学习成本
- **可扩展性**：模块化的设计支持灵活的功能扩展
- **社区支持**：活跃的开发者社区提供持续的支持

## 2. 技术架构

### 核心架构
项目采用现代化的技术架构，主要包含以下几个层次：

1. **表现层**：负责用户界面的展示和交互
2. **业务层**：处理核心业务逻辑
3. **数据层**：管理数据存储和访问

### 关键技术
- 前端技术栈
- 后端服务架构
- 数据库设计
- 缓存策略

## 3. 性能分析

### 性能指标
- **响应时间**：平均响应时间 < 100ms
- **并发处理**：支持 10,000+ 并发用户
- **资源占用**：内存使用优化，CPU占用率低

### 优化策略
1. 代码优化
2. 缓存机制
3. 数据库优化
4. CDN加速

## 4. 生态系统

### 相关工具
- 开发工具链
- 测试框架
- 部署工具
- 监控系统

### 社区资源
- 官方文档
- 社区教程
- 开源插件
- 技术支持

## 5. 总结与建议

### 优势
- 技术先进性
- 性能优异
- 社区活跃
- 文档完善

### 适用场景
- 大型企业应用
- 高并发系统
- 实时数据处理
- 微服务架构

### 学习建议
1. 掌握基础概念
2. 实践项目开发
3. 参与社区讨论
4. 持续学习新特性

---

*报告生成时间：${this.updatedAt}*
*研究员：${this.creator}*`;

                        // 使用marked库解析Markdown
                        this.markdownContent = marked.parse(mockMarkdown);

                    } catch (error) {
                        console.error('加载Markdown内容失败:', error);
                        this.markdownContent = '<p class="text-red-600">加载报告内容失败</p>';
                    } finally {
                        this.markdownLoading = false;
                    }
                },

                viewAnalysis() {
                    // 跳转到分析页面
                    window.open(`analysis.html?id=${this.reportId}`, '_blank');
                },

                retryLoad() {
                    this.error = false;
                    this.loading = true;
                    this.loadReportData();
                },

                goBack() {
                    // 返回报告列表
                    window.history.back();
                }
            }
        }
    </script>
</body>
</html>
