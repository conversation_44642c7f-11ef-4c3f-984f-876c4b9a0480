<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登录</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .btn-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: all 0.3s ease;
        }
        .btn-gradient:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        .glass-effect {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .input-focus {
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        .floating-shapes {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 0;
        }
        .floating-shapes::before,
        .floating-shapes::after {
            content: '';
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            animation: float 6s ease-in-out infinite;
        }
        .floating-shapes::before {
            width: 200px;
            height: 200px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }
        .floating-shapes::after {
            width: 150px;
            height: 150px;
            bottom: 20%;
            right: 10%;
            animation-delay: 3s;
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
    </style>
</head>
<body class="min-h-screen gradient-bg flex items-center justify-center p-4">
    <div class="floating-shapes"></div>
    
    <div class="w-full max-w-md relative z-10" x-data="loginData()">
        <!-- 登录卡片 -->
        <div class="glass-effect rounded-2xl shadow-2xl p-8">
            <!-- Logo和标题 -->
            <div class="text-center mb-8">
                <div class="w-16 h-16 bg-white bg-opacity-20 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-shield-alt text-white text-2xl"></i>
                </div>
                <h1 class="text-2xl font-bold text-white mb-2">管理员登录</h1>
                <p class="text-white text-opacity-80">请输入您的管理员凭据</p>
            </div>

            <!-- 登录表单 -->
            <form @submit.prevent="login()" class="space-y-6">
                <!-- 邮箱输入 -->
                <div>
                    <label class="block text-white text-sm font-semibold mb-2">
                        <i class="fas fa-envelope mr-2"></i>
                        邮箱地址
                    </label>
                    <div class="relative">
                        <input type="email" 
                               x-model="formData.email"
                               placeholder="<EMAIL>"
                               class="w-full px-4 py-3 pl-12 bg-white bg-opacity-20 border border-white border-opacity-30 rounded-xl text-white placeholder-white placeholder-opacity-70 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 focus:border-transparent transition-all"
                               required>
                        <i class="fas fa-envelope absolute left-4 top-1/2 transform -translate-y-1/2 text-white text-opacity-70"></i>
                    </div>
                </div>

                <!-- 密码输入 -->
                <div>
                    <label class="block text-white text-sm font-semibold mb-2">
                        <i class="fas fa-lock mr-2"></i>
                        密码
                    </label>
                    <div class="relative">
                        <input :type="showPassword ? 'text' : 'password'" 
                               x-model="formData.password"
                               placeholder="请输入密码"
                               class="w-full px-4 py-3 pl-12 pr-12 bg-white bg-opacity-20 border border-white border-opacity-30 rounded-xl text-white placeholder-white placeholder-opacity-70 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 focus:border-transparent transition-all"
                               required>
                        <i class="fas fa-lock absolute left-4 top-1/2 transform -translate-y-1/2 text-white text-opacity-70"></i>
                        <button type="button" 
                                @click="showPassword = !showPassword"
                                class="absolute right-4 top-1/2 transform -translate-y-1/2 text-white text-opacity-70 hover:text-opacity-100 transition-all">
                            <i :class="showPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
                        </button>
                    </div>
                </div>

                <!-- 记住我 -->
                <div class="flex items-center justify-between">
                    <label class="flex items-center text-white text-sm">
                        <input type="checkbox" 
                               x-model="formData.remember"
                               class="mr-2 rounded border-white border-opacity-30 bg-white bg-opacity-20 text-blue-600 focus:ring-white focus:ring-opacity-50">
                        记住我
                    </label>
                    <a href="#" class="text-white text-opacity-80 hover:text-opacity-100 text-sm transition-all">
                        忘记密码？
                    </a>
                </div>

                <!-- 登录按钮 -->
                <button type="submit" 
                        :disabled="loading"
                        class="w-full bg-white text-gray-800 py-3 px-4 rounded-xl font-semibold hover:bg-opacity-90 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 transition-all transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed">
                    <span x-show="!loading" class="flex items-center justify-center">
                        <i class="fas fa-sign-in-alt mr-2"></i>
                        登录
                    </span>
                    <span x-show="loading" class="flex items-center justify-center">
                        <i class="fas fa-spinner fa-spin mr-2"></i>
                        登录中...
                    </span>
                </button>
            </form>

            <!-- 错误提示 -->
            <div x-show="error" 
                 x-transition
                 class="mt-4 bg-red-500 bg-opacity-20 border border-red-500 border-opacity-30 rounded-xl p-3">
                <div class="flex items-center text-red-100">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    <span x-text="error"></span>
                </div>
            </div>

            <!-- 成功提示 -->
            <div x-show="success" 
                 x-transition
                 class="mt-4 bg-green-500 bg-opacity-20 border border-green-500 border-opacity-30 rounded-xl p-3">
                <div class="flex items-center text-green-100">
                    <i class="fas fa-check-circle mr-2"></i>
                    <span>登录成功，正在跳转...</span>
                </div>
            </div>

            <!-- 快速登录提示 -->
            <div class="mt-6 p-4 bg-white bg-opacity-10 rounded-xl">
                <h4 class="text-white font-semibold mb-2 flex items-center">
                    <i class="fas fa-info-circle mr-2"></i>
                    演示账户
                </h4>
                <div class="text-white text-opacity-80 text-sm space-y-1">
                    <p>邮箱：<EMAIL></p>
                    <p>密码：admin123</p>
                </div>
                <button @click="quickLogin()" 
                        class="mt-3 w-full bg-white bg-opacity-20 text-white py-2 px-4 rounded-lg text-sm hover:bg-opacity-30 transition-all">
                    <i class="fas fa-bolt mr-2"></i>
                    快速登录
                </button>
            </div>
        </div>

        <!-- 底部链接 -->
        <div class="text-center mt-6">
            <a href="#" class="text-white text-opacity-80 hover:text-opacity-100 text-sm transition-all">
                <i class="fas fa-arrow-left mr-2"></i>
                返回首页
            </a>
        </div>
    </div>

    <!-- 安全提示浮窗 -->
    <div x-show="showSecurityTip" 
         x-transition
         class="fixed bottom-4 right-4 bg-white rounded-xl shadow-lg p-4 max-w-sm z-20">
        <div class="flex items-start">
            <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                <i class="fas fa-shield-alt text-yellow-600"></i>
            </div>
            <div>
                <h4 class="font-semibold text-gray-800 mb-1">安全提示</h4>
                <p class="text-sm text-gray-600 mb-3">请确保您在安全的网络环境中登录管理后台。</p>
                <button @click="showSecurityTip = false" 
                        class="text-blue-600 text-sm hover:underline">
                    我知道了
                </button>
            </div>
        </div>
    </div>

    <script>
        function loginData() {
            return {
                formData: {
                    email: '',
                    password: '',
                    remember: false
                },
                showPassword: false,
                loading: false,
                error: '',
                success: false,
                showSecurityTip: true,
                
                init() {
                    // 5秒后自动隐藏安全提示
                    setTimeout(() => {
                        this.showSecurityTip = false;
                    }, 5000);
                },
                
                async login() {
                    this.loading = true;
                    this.error = '';
                    
                    try {
                        // 模拟登录请求
                        await new Promise(resolve => setTimeout(resolve, 1500));
                        
                        // 验证演示账户
                        if (this.formData.email === '<EMAIL>' && this.formData.password === 'admin123') {
                            this.success = true;
                            setTimeout(() => {
                                // 这里可以跳转到管理仪表板
                                console.log('登录成功，跳转到仪表板');
                            }, 1000);
                        } else {
                            this.error = '邮箱或密码错误，请检查后重试';
                        }
                    } catch (err) {
                        this.error = '登录失败，请稍后重试';
                    } finally {
                        this.loading = false;
                    }
                },
                
                quickLogin() {
                    this.formData.email = '<EMAIL>';
                    this.formData.password = 'admin123';
                    this.login();
                }
            }
        }
    </script>
</body>
</html>
