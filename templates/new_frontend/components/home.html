<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页 - 项目研究报告列表</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        .btn-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: all 0.3s ease;
        }
        .btn-gradient:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        .search-focus {
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="max-w-6xl mx-auto p-6" x-data="homeData()">
        <!-- 页面头部 -->
        <div class="mb-8">
            <div class="text-center mb-6">
                <h1 class="text-3xl font-bold text-gray-800 mb-2">
                    <i class="fas fa-chart-line text-blue-600 mr-3"></i>
                    项目调研报告
                </h1>
                <p class="text-gray-600">浏览已完成的项目调研报告，获取深度技术分析</p>
            </div>

            <!-- 搜索和操作栏 -->
            <div class="flex flex-col md:flex-row gap-4 items-center justify-between mb-6">
                <!-- 搜索框 -->
                <div class="flex-1 max-w-2xl">
                    <div class="relative">
                        <input type="text"
                               x-model="searchQuery"
                               @input="filterReports"
                               placeholder="搜索项目名称..."
                               class="w-full px-4 py-3 pl-12 pr-4 rounded-2xl border border-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all">
                        <i class="fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                    </div>
                </div>

                <!-- 找不到项目链接 -->
                <div class="flex items-center space-x-4">
                    <button @click="showRequestModal = true"
                            class="text-blue-600 hover:text-blue-800 font-medium transition-colors">
                        <i class="fas fa-question-circle mr-2"></i>
                        找不到项目？
                    </button>
                </div>
            </div>

            <!-- 提示信息 -->
            <div class="bg-blue-50 border border-blue-200 rounded-xl p-4 mb-6">
                <div class="flex items-start">
                    <i class="fas fa-info-circle text-blue-600 mt-1 mr-3"></i>
                    <div>
                        <p class="text-blue-800 text-sm">
                            请提交你想要调研的项目，算力因素报告产出可能会有延迟，完成后我们会通过邮箱通知，谢谢！
                        </p>
                    </div>
                </div>
            </div>

            <!-- 统计信息 -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="bg-white rounded-2xl p-4 shadow-sm border border-gray-100">
                    <div class="flex items-center">
                        <div class="bg-blue-100 p-3 rounded-xl">
                            <i class="fas fa-file-alt text-blue-600"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm text-gray-600">总报告数</p>
                            <p class="text-2xl font-bold text-gray-800" x-text="reports.length"></p>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-2xl p-4 shadow-sm border border-gray-100">
                    <div class="flex items-center">
                        <div class="bg-green-100 p-3 rounded-xl">
                            <i class="fas fa-clock text-green-600"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm text-gray-600">待处理请求</p>
                            <p class="text-2xl font-bold text-gray-800">3</p>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-2xl p-4 shadow-sm border border-gray-100">
                    <div class="flex items-center">
                        <div class="bg-purple-100 p-3 rounded-xl">
                            <i class="fas fa-star text-purple-600"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm text-gray-600">本月新增</p>
                            <p class="text-2xl font-bold text-gray-800">5</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 报告列表 -->
        <div class="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
            <!-- 表格头部 -->
            <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <h3 class="text-lg font-semibold text-gray-800">
                    <i class="fas fa-list mr-2"></i>
                    调研报告列表
                </h3>
            </div>

            <!-- 表格内容 -->
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50 border-b border-gray-200">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目名称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建人</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">更新时间</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                            <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <template x-for="report in filteredReports" :key="report.id">
                            <tr class="hover:bg-gray-50 transition-colors">
                                <!-- 项目名称 -->
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <div class="h-10 w-10 rounded-lg bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center">
                                                <i class="fas fa-project-diagram text-white text-sm"></i>
                                            </div>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900" x-text="report.name"></div>
                                            <div class="text-sm text-gray-500" x-text="report.category"></div>
                                        </div>
                                    </div>
                                </td>

                                <!-- 创建人 -->
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="h-8 w-8 bg-gray-200 rounded-full flex items-center justify-center mr-2">
                                            <i class="fas fa-user text-gray-600 text-xs"></i>
                                        </div>
                                        <span class="text-sm text-gray-900" x-text="report.creator"></span>
                                    </div>
                                </td>

                                <!-- 创建时间 -->
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <div class="flex items-center">
                                        <i class="fas fa-calendar-plus mr-2 text-gray-400"></i>
                                        <span x-text="report.created_at"></span>
                                    </div>
                                </td>

                                <!-- 更新时间 -->
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <div class="flex items-center">
                                        <i class="fas fa-calendar-check mr-2 text-gray-400"></i>
                                        <span x-text="report.updated_at"></span>
                                    </div>
                                </td>

                                <!-- 状态 -->
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                        <i class="fas fa-check-circle mr-1"></i>
                                        已完成
                                    </span>
                                </td>

                                <!-- 操作按钮 -->
                                <td class="px-6 py-4 whitespace-nowrap text-center">
                                    <div class="flex items-center justify-center space-x-2">
                                        <button @click="viewAnalysis(report.id)"
                                                class="bg-blue-50 text-blue-600 px-3 py-2 rounded-lg text-xs font-medium hover:bg-blue-100 transition-colors">
                                            <i class="fas fa-chart-bar mr-1"></i>
                                            查看分析页面
                                        </button>
                                        <button @click="viewReport(report.id)"
                                                class="bg-gray-50 text-gray-600 px-3 py-2 rounded-lg text-xs font-medium hover:bg-gray-100 transition-colors">
                                            <i class="fas fa-file-alt mr-1"></i>
                                            查看分析报告
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </template>
                    </tbody>
                </table>
            </div>

            <!-- 空状态 -->
            <div x-show="filteredReports.length === 0" class="text-center py-12">
                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-search text-gray-400 text-xl"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">未找到相关报告</h3>
                <p class="text-gray-500 mb-4">尝试调整搜索关键词或提交新的项目申请</p>
                <button @click="showRequestModal = true"
                        class="btn-gradient text-white px-4 py-2 rounded-xl font-medium">
                    <i class="fas fa-plus mr-2"></i>
                    申请新项目
                </button>
            </div>
        </div>

        <!-- 分页 -->
        <div class="flex justify-center mt-8">
            <div class="flex items-center space-x-2">
                <button class="px-3 py-2 rounded-lg border border-gray-200 text-gray-600 hover:bg-gray-50 transition-colors">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <button class="px-4 py-2 rounded-lg bg-blue-600 text-white font-medium">1</button>
                <button class="px-4 py-2 rounded-lg border border-gray-200 text-gray-600 hover:bg-gray-50 transition-colors">2</button>
                <button class="px-4 py-2 rounded-lg border border-gray-200 text-gray-600 hover:bg-gray-50 transition-colors">3</button>
                <button class="px-3 py-2 rounded-lg border border-gray-200 text-gray-600 hover:bg-gray-50 transition-colors">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </div>

        <!-- 项目申请弹窗 -->
        <div x-show="showRequestModal"
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div x-show="showRequestModal"
                 x-transition:enter="transition ease-out duration-300"
                 x-transition:enter-start="opacity-0 transform scale-95"
                 x-transition:enter-end="opacity-100 transform scale-100"
                 x-transition:leave="transition ease-in duration-200"
                 x-transition:leave-start="opacity-100 transform scale-100"
                 x-transition:leave-end="opacity-0 transform scale-95"
                 @click.away="showRequestModal = false"
                 class="bg-white rounded-2xl shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">

                <!-- 弹窗头部 -->
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900">
                            <i class="fas fa-plus-circle text-blue-600 mr-2"></i>
                            申请新项目调研
                        </h3>
                        <button @click="showRequestModal = false"
                                class="text-gray-400 hover:text-gray-600 transition-colors">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>

                <!-- 弹窗内容 -->
                <form @submit.prevent="submitRequest()" class="p-6 space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            您的邮箱 <span class="text-red-500">*</span>
                        </label>
                        <input type="email"
                               x-model="requestForm.email"
                               placeholder="<EMAIL>"
                               required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            项目名称 <span class="text-red-500">*</span>
                        </label>
                        <input type="text"
                               x-model="requestForm.projectName"
                               placeholder="例如：React.js"
                               required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            官网主页链接 <span class="text-red-500">*</span>
                        </label>
                        <input type="url"
                               x-model="requestForm.website"
                               placeholder="https://example.com"
                               required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            补充说明 <span class="text-gray-500">(可选)</span>
                        </label>
                        <textarea x-model="requestForm.description"
                                  placeholder="请简单描述您希望我们重点关注的方面..."
                                  rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"></textarea>
                    </div>

                    <!-- 提交按钮 -->
                    <div class="flex space-x-3 pt-4">
                        <button type="button"
                                @click="showRequestModal = false"
                                class="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                            取消
                        </button>
                        <button type="submit"
                                :disabled="submitting"
                                class="flex-1 btn-gradient text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed">
                            <span x-show="!submitting">提交申请</span>
                            <span x-show="submitting" class="flex items-center justify-center">
                                <i class="fas fa-spinner fa-spin mr-2"></i>
                                提交中...
                            </span>
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 成功提示弹窗 -->
        <div x-show="showSuccessModal"
             x-transition
             class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div class="bg-white rounded-2xl shadow-xl max-w-sm w-full p-6 text-center">
                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-check text-green-600 text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">申请提交成功！</h3>
                <p class="text-gray-600 mb-4">我们已收到您的申请，完成后会通过邮箱通知您。</p>
                <button @click="showSuccessModal = false"
                        class="btn-gradient text-white px-6 py-2 rounded-lg font-medium">
                    确定
                </button>
            </div>
        </div>
    </div>

    <script>
        function homeData() {
            return {
                searchQuery: '',
                showRequestModal: false,
                showSuccessModal: false,
                submitting: false,
                requestForm: {
                    email: '',
                    projectName: '',
                    website: '',
                    description: ''
                },
                reports: [
                    {
                        id: 1,
                        name: 'React.js',
                        creator: '张三',
                        created_at: '2024-01-15',
                        updated_at: '2024-01-20',
                        category: '前端框架',
                        website: 'https://reactjs.org',
                        status: 'completed'
                    },
                    {
                        id: 2,
                        name: 'Vue.js',
                        creator: '李四',
                        created_at: '2024-01-10',
                        updated_at: '2024-01-18',
                        category: '前端框架',
                        website: 'https://vuejs.org',
                        status: 'completed'
                    },
                    {
                        id: 3,
                        name: 'Node.js',
                        creator: '王五',
                        created_at: '2024-01-08',
                        updated_at: '2024-01-16',
                        category: '后端技术',
                        website: 'https://nodejs.org',
                        status: 'completed'
                    },
                    {
                        id: 4,
                        name: 'Docker',
                        creator: '赵六',
                        created_at: '2024-01-05',
                        updated_at: '2024-01-14',
                        category: '容器技术',
                        website: 'https://docker.com',
                        status: 'completed'
                    },
                    {
                        id: 5,
                        name: 'Kubernetes',
                        creator: '钱七',
                        created_at: '2024-01-03',
                        updated_at: '2024-01-12',
                        category: '容器编排',
                        website: 'https://kubernetes.io',
                        status: 'completed'
                    },
                    {
                        id: 6,
                        name: 'TensorFlow',
                        creator: '孙八',
                        created_at: '2024-01-01',
                        updated_at: '2024-01-10',
                        category: '机器学习',
                        website: 'https://tensorflow.org',
                        status: 'completed'
                    }
                ],
                filteredReports: [],

                init() {
                    this.filteredReports = this.reports;
                },

                filterReports() {
                    if (this.searchQuery.trim() === '') {
                        this.filteredReports = this.reports;
                    } else {
                        this.filteredReports = this.reports.filter(report =>
                            report.name.toLowerCase().includes(this.searchQuery.toLowerCase())
                        );
                    }
                },

                viewAnalysis(reportId) {
                    // 跳转到分析页面 - 渲染HTML文件
                    window.open(`analysis.html?id=${reportId}`, '_blank');
                },

                viewReport(reportId) {
                    // 跳转到报告页面 - 渲染Markdown文件
                    window.open(`report-detail.html?id=${reportId}`, '_blank');
                },

                async submitRequest() {
                    if (!this.requestForm.email || !this.requestForm.projectName || !this.requestForm.website) {
                        alert('请填写所有必填字段');
                        return;
                    }

                    this.submitting = true;

                    try {
                        // 模拟API调用
                        await new Promise(resolve => setTimeout(resolve, 1500));

                        // 这里应该调用实际的API
                        // const response = await fetch('/api/request-project', {
                        //     method: 'POST',
                        //     headers: {
                        //         'Content-Type': 'application/json',
                        //     },
                        //     body: JSON.stringify(this.requestForm)
                        // });

                        // 重置表单
                        this.requestForm = {
                            email: '',
                            projectName: '',
                            website: '',
                            description: ''
                        };

                        this.showRequestModal = false;
                        this.showSuccessModal = true;

                    } catch (error) {
                        console.error('提交失败:', error);
                        alert('提交失败，请稍后重试');
                    } finally {
                        this.submitting = false;
                    }
                }
            }
        }
    </script>
</body>
</html>
