<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>React.js 交互式分析页面</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .metric-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        .metric-card.blue {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        .metric-card.green {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
        .metric-card.purple {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen p-6">
    <div class="max-w-7xl mx-auto">
        <!-- 页面标题 -->
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-800 mb-2">
                <i class="fas fa-chart-bar text-blue-600 mr-3"></i>
                React.js 交互式数据分析
            </h1>
            <p class="text-gray-600">这是一个演示用的交互式分析页面，展示项目的各种数据指标和可视化图表</p>
        </div>

        <!-- 核心指标卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="metric-card blue text-white p-6 rounded-2xl card-hover">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-white text-opacity-80 text-sm">GitHub Stars</p>
                        <p class="text-3xl font-bold">220.5K</p>
                        <p class="text-white text-opacity-80 text-sm mt-1">
                            <i class="fas fa-arrow-up mr-1"></i>
                            +2.3% 本月
                        </p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-3 rounded-xl">
                        <i class="fas fa-star text-2xl"></i>
                    </div>
                </div>
            </div>

            <div class="metric-card green text-white p-6 rounded-2xl card-hover">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-white text-opacity-80 text-sm">周下载量</p>
                        <p class="text-3xl font-bold">18.5M</p>
                        <p class="text-white text-opacity-80 text-sm mt-1">
                            <i class="fas fa-arrow-up mr-1"></i>
                            +5.7% 本周
                        </p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-3 rounded-xl">
                        <i class="fas fa-download text-2xl"></i>
                    </div>
                </div>
            </div>

            <div class="metric-card purple text-white p-6 rounded-2xl card-hover">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-white text-opacity-80 text-sm">活跃贡献者</p>
                        <p class="text-3xl font-bold">1,547</p>
                        <p class="text-white text-opacity-80 text-sm mt-1">
                            <i class="fas fa-arrow-up mr-1"></i>
                            +12 本月
                        </p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-3 rounded-xl">
                        <i class="fas fa-users text-2xl"></i>
                    </div>
                </div>
            </div>

            <div class="metric-card text-white p-6 rounded-2xl card-hover">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-white text-opacity-80 text-sm">Issues 解决率</p>
                        <p class="text-3xl font-bold">94.2%</p>
                        <p class="text-white text-opacity-80 text-sm mt-1">
                            <i class="fas fa-arrow-up mr-1"></i>
                            +1.2% 本月
                        </p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-3 rounded-xl">
                        <i class="fas fa-check-circle text-2xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图表区域 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <!-- 下载趋势图 -->
            <div class="bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-bold text-gray-800">
                        <i class="fas fa-chart-line text-blue-600 mr-2"></i>
                        下载趋势
                    </h3>
                    <select class="border border-gray-200 rounded-lg px-3 py-1 text-sm">
                        <option>最近30天</option>
                        <option>最近90天</option>
                        <option>最近一年</option>
                    </select>
                </div>
                <canvas id="downloadChart" class="w-full h-64"></canvas>
            </div>

            <!-- 版本分布图 -->
            <div class="bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-bold text-gray-800">
                        <i class="fas fa-pie-chart text-green-600 mr-2"></i>
                        版本使用分布
                    </h3>
                    <button class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-info-circle"></i>
                    </button>
                </div>
                <canvas id="versionChart" class="w-full h-64"></canvas>
            </div>
        </div>

        <!-- 详细分析 -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
            <!-- 性能指标 -->
            <div class="bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
                <h3 class="text-lg font-bold text-gray-800 mb-4">
                    <i class="fas fa-tachometer-alt text-purple-600 mr-2"></i>
                    性能指标
                </h3>
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <span class="text-gray-600">Bundle 大小</span>
                        <span class="font-semibold text-gray-800">42.2KB</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-green-500 h-2 rounded-full" style="width: 85%"></div>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-gray-600">加载速度</span>
                        <span class="font-semibold text-gray-800">1.2s</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-blue-500 h-2 rounded-full" style="width: 92%"></div>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-gray-600">内存使用</span>
                        <span class="font-semibold text-gray-800">15.8MB</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-yellow-500 h-2 rounded-full" style="width: 78%"></div>
                    </div>
                </div>
            </div>

            <!-- 社区活跃度 -->
            <div class="bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
                <h3 class="text-lg font-bold text-gray-800 mb-4">
                    <i class="fas fa-users text-orange-600 mr-2"></i>
                    社区活跃度
                </h3>
                <div class="space-y-4">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-code text-blue-600"></i>
                        </div>
                        <div>
                            <p class="font-semibold text-gray-800">1,247</p>
                            <p class="text-sm text-gray-600">本月提交</p>
                        </div>
                    </div>
                    
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-bug text-green-600"></i>
                        </div>
                        <div>
                            <p class="font-semibold text-gray-800">89</p>
                            <p class="text-sm text-gray-600">已修复问题</p>
                        </div>
                    </div>
                    
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-comments text-purple-600"></i>
                        </div>
                        <div>
                            <p class="font-semibold text-gray-800">456</p>
                            <p class="text-sm text-gray-600">讨论参与</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 技术栈分析 -->
            <div class="bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
                <h3 class="text-lg font-bold text-gray-800 mb-4">
                    <i class="fas fa-layer-group text-red-600 mr-2"></i>
                    技术栈分析
                </h3>
                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-yellow-500 rounded-full mr-2"></div>
                            <span class="text-sm text-gray-600">JavaScript</span>
                        </div>
                        <span class="text-sm font-semibold">67.8%</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                            <span class="text-sm text-gray-600">TypeScript</span>
                        </div>
                        <span class="text-sm font-semibold">18.2%</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                            <span class="text-sm text-gray-600">CSS</span>
                        </div>
                        <span class="text-sm font-semibold">8.9%</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                            <span class="text-sm text-gray-600">HTML</span>
                        </div>
                        <span class="text-sm font-semibold">3.7%</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-gray-500 rounded-full mr-2"></div>
                            <span class="text-sm text-gray-600">其他</span>
                        </div>
                        <span class="text-sm font-semibold">1.4%</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 交互式功能演示 -->
        <div class="bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
            <h3 class="text-lg font-bold text-gray-800 mb-6">
                <i class="fas fa-mouse-pointer text-indigo-600 mr-2"></i>
                交互式功能演示
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button onclick="showAlert('点击了实时数据更新')" 
                        class="p-4 bg-blue-50 rounded-xl hover:bg-blue-100 transition-colors text-left">
                    <i class="fas fa-sync-alt text-blue-600 mb-2"></i>
                    <h4 class="font-semibold text-gray-800">实时数据更新</h4>
                    <p class="text-sm text-gray-600">点击刷新最新数据</p>
                </button>
                
                <button onclick="showAlert('点击了数据导出')" 
                        class="p-4 bg-green-50 rounded-xl hover:bg-green-100 transition-colors text-left">
                    <i class="fas fa-download text-green-600 mb-2"></i>
                    <h4 class="font-semibold text-gray-800">数据导出</h4>
                    <p class="text-sm text-gray-600">导出分析报告</p>
                </button>
                
                <button onclick="showAlert('点击了自定义筛选')" 
                        class="p-4 bg-purple-50 rounded-xl hover:bg-purple-100 transition-colors text-left">
                    <i class="fas fa-filter text-purple-600 mb-2"></i>
                    <h4 class="font-semibold text-gray-800">自定义筛选</h4>
                    <p class="text-sm text-gray-600">设置数据筛选条件</p>
                </button>
            </div>
        </div>
    </div>

    <script>
        // 初始化图表
        document.addEventListener('DOMContentLoaded', function() {
            // 下载趋势图
            const downloadCtx = document.getElementById('downloadChart').getContext('2d');
            new Chart(downloadCtx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                    datasets: [{
                        label: '周下载量 (百万)',
                        data: [12, 14, 16, 15, 17, 18.5],
                        borderColor: 'rgb(102, 126, 234)',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });

            // 版本分布图
            const versionCtx = document.getElementById('versionChart').getContext('2d');
            new Chart(versionCtx, {
                type: 'doughnut',
                data: {
                    labels: ['v18.x', 'v17.x', 'v16.x', '其他'],
                    datasets: [{
                        data: [65, 20, 12, 3],
                        backgroundColor: [
                            'rgb(102, 126, 234)',
                            'rgb(34, 197, 94)',
                            'rgb(251, 146, 60)',
                            'rgb(156, 163, 175)'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true
                            }
                        }
                    }
                }
            });
        });

        function showAlert(message) {
            alert('交互式功能演示：' + message);
        }
    </script>
</body>
</html>
