<!DOCTYPE html>
<html lang="zh-CN" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="专业的项目研究报告平台，提供深度技术分析和项目评估">
    <title>项目研究报告平台 - 高保真原型展示</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <!-- 自定义样式 -->
    <link rel="stylesheet" href="assets/styles.css">
    
    <style>
        /* 自定义样式 */
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .glass-effect {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        .text-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .btn-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: all 0.3s ease;
        }
        .btn-gradient:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        .iframe-container {
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        .iframe-container:hover {
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }
        .nav-item {
            transition: all 0.3s ease;
            border-radius: 12px;
            padding: 12px 20px;
            margin: 0 4px;
        }
        .nav-item:hover {
            background: rgba(102, 126, 234, 0.1);
            transform: translateY(-2px);
        }
        .nav-item.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 顶部导航栏 -->
    <nav class="gradient-bg text-white shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center space-x-3">
                    <i class="fas fa-chart-line text-2xl"></i>
                    <h1 class="text-xl font-bold">项目研究报告平台</h1>
                </div>
                <div class="hidden md:flex items-center space-x-1" x-data="{ activeTab: 'home' }">
                    <button @click="activeTab = 'home'; showPage('home')" 
                            :class="activeTab === 'home' ? 'nav-item active' : 'nav-item'"
                            class="flex items-center space-x-2">
                        <i class="fas fa-home"></i>
                        <span>首页</span>
                    </button>
                    <button @click="activeTab = 'reports'; showPage('reports')" 
                            :class="activeTab === 'reports' ? 'nav-item active' : 'nav-item'"
                            class="flex items-center space-x-2">
                        <i class="fas fa-file-alt"></i>
                        <span>报告详情</span>
                    </button>
                    <button @click="activeTab = 'analysis'; showPage('analysis')" 
                            :class="activeTab === 'analysis' ? 'nav-item active' : 'nav-item'"
                            class="flex items-center space-x-2">
                        <i class="fas fa-chart-bar"></i>
                        <span>数据分析</span>
                    </button>
                    <button @click="activeTab = 'request'; showPage('request')" 
                            :class="activeTab === 'request' ? 'nav-item active' : 'nav-item'"
                            class="flex items-center space-x-2">
                        <i class="fas fa-plus-circle"></i>
                        <span>申请项目</span>
                    </button>
                    <button @click="activeTab = 'admin'; showPage('admin')" 
                            :class="activeTab === 'admin' ? 'nav-item active' : 'nav-item'"
                            class="flex items-center space-x-2">
                        <i class="fas fa-cog"></i>
                        <span>管理后台</span>
                    </button>
                </div>
                <div class="flex items-center space-x-4">
                    <button class="p-2 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all">
                        <i class="fas fa-bell"></i>
                    </button>
                    <button class="p-2 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all">
                        <i class="fas fa-user-circle text-xl"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 页面标题和描述 -->
        <div class="text-center mb-12">
            <h2 class="text-4xl font-bold text-gradient mb-4">高保真原型展示</h2>
            <p class="text-gray-600 text-lg max-w-3xl mx-auto">
                以下展示了项目调研报告平台的所有核心界面，根据最新功能需求重新设计。
                包含调研报告列表、交互式分析页面、Markdown报告展示、项目申请流程等完整功能。
                每个界面都经过精心设计，确保在实际开发中可以直接使用。
            </p>
        </div>

        <!-- 页面展示区域 -->
        <div class="space-y-16">
            <!-- 调研报告列表页展示 -->
            <section id="home-section" class="page-section">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-2xl font-bold text-gray-800 flex items-center">
                        <i class="fas fa-list text-blue-600 mr-3"></i>
                        调研报告列表页
                    </h3>
                    <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                        访客默认页面
                    </span>
                </div>
                <div class="iframe-container">
                    <iframe src="components/home.html"
                            class="w-full h-96 border-0"
                            title="调研报告列表页展示"></iframe>
                </div>
                <div class="mt-4 p-4 bg-blue-50 rounded-xl">
                    <h4 class="font-semibold text-blue-800 mb-2">功能特色</h4>
                    <ul class="text-sm text-blue-700 space-y-1">
                        <li>• 表格形式展示项目名、创建人、创建时间、更新时间</li>
                        <li>• 提供「查看分析页面」和「查看分析报告」两个操作</li>
                        <li>• 支持项目名模糊搜索（大小写不敏感）</li>
                        <li>• 「找不到项目？」链接打开项目申请弹窗</li>
                    </ul>
                </div>
            </section>

            <!-- Markdown报告详情页展示 -->
            <section id="reports-section" class="page-section">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-2xl font-bold text-gray-800 flex items-center">
                        <i class="fas fa-file-alt text-green-600 mr-3"></i>
                        Markdown报告页
                    </h3>
                    <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                        查看分析报告
                    </span>
                </div>
                <div class="iframe-container">
                    <iframe src="components/report-detail.html"
                            class="w-full h-96 border-0"
                            title="Markdown报告页展示"></iframe>
                </div>
                <div class="mt-4 p-4 bg-green-50 rounded-xl">
                    <h4 class="font-semibold text-green-800 mb-2">功能特色</h4>
                    <ul class="text-sm text-green-700 space-y-1">
                        <li>• 动态加载并渲染Markdown文件内容</li>
                        <li>• 美观的Markdown样式展示</li>
                        <li>• 支持报告下载和分享功能</li>
                        <li>• 显示报告创建和更新时间信息</li>
                    </ul>
                </div>
            </section>

            <!-- 交互式分析页展示 -->
            <section id="analysis-section" class="page-section">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-2xl font-bold text-gray-800 flex items-center">
                        <i class="fas fa-chart-bar text-purple-600 mr-3"></i>
                        交互式分析页
                    </h3>
                    <span class="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm font-medium">
                        查看分析页面
                    </span>
                </div>
                <div class="iframe-container">
                    <iframe src="components/analysis.html"
                            class="w-full h-96 border-0"
                            title="交互式分析页展示"></iframe>
                </div>
                <div class="mt-4 p-4 bg-purple-50 rounded-xl">
                    <h4 class="font-semibold text-purple-800 mb-2">功能特色</h4>
                    <ul class="text-sm text-purple-700 space-y-1">
                        <li>• 通过iframe渲染项目的交互式HTML分析文件</li>
                        <li>• 支持图表、数据可视化等交互式内容</li>
                        <li>• 自适应iframe高度，优化显示效果</li>
                        <li>• 提供分析页面导出和分享功能</li>
                    </ul>
                </div>
            </section>

            <!-- 项目申请功能展示 -->
            <section id="request-section" class="page-section">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-2xl font-bold text-gray-800 flex items-center">
                        <i class="fas fa-plus-circle text-orange-600 mr-3"></i>
                        项目申请功能
                    </h3>
                    <span class="bg-orange-100 text-orange-800 px-3 py-1 rounded-full text-sm font-medium">
                        信息收集弹窗
                    </span>
                </div>
                <div class="iframe-container">
                    <iframe src="components/project-request.html"
                            class="w-full h-96 border-0"
                            title="项目申请功能展示"></iframe>
                </div>
                <div class="mt-4 p-4 bg-orange-50 rounded-xl">
                    <h4 class="font-semibold text-orange-800 mb-2">功能特色</h4>
                    <ul class="text-sm text-orange-700 space-y-1">
                        <li>• 通过「找不到项目？」链接触发弹窗</li>
                        <li>• 收集用户邮箱、项目名称、官网链接</li>
                        <li>• 提交后记录到数据库并通知维护方</li>
                        <li>• 完成后通过邮件通知用户报告已生成</li>
                    </ul>
                </div>
            </section>

            <!-- 管理后台展示 -->
            <section id="admin-section" class="page-section">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-2xl font-bold text-gray-800 flex items-center">
                        <i class="fas fa-cog text-red-600 mr-3"></i>
                        管理后台
                    </h3>
                    <span class="bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-medium">
                        管理员专用
                    </span>
                </div>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div class="iframe-container">
                        <h4 class="text-lg font-semibold mb-3 text-gray-700">登录页面</h4>
                        <iframe src="components/admin-login.html" 
                                class="w-full h-80 border-0" 
                                title="管理员登录页"></iframe>
                    </div>
                    <div class="iframe-container">
                        <h4 class="text-lg font-semibold mb-3 text-gray-700">管理仪表板</h4>
                        <iframe src="components/admin-dashboard.html" 
                                class="w-full h-80 border-0" 
                                title="管理仪表板"></iframe>
                    </div>
                </div>
            </section>
        </div>

        <!-- 底部说明 -->
        <div class="mt-16 bg-white rounded-2xl p-8 shadow-lg">
            <h3 class="text-2xl font-bold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-info-circle text-blue-600 mr-3"></i>
                设计说明
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div class="space-y-2">
                    <h4 class="font-semibold text-gray-700">🎨 设计特色</h4>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• 现代化渐变色彩</li>
                        <li>• 圆角和柔和阴影</li>
                        <li>• 微交互动画效果</li>
                        <li>• 玻璃拟态设计</li>
                    </ul>
                </div>
                <div class="space-y-2">
                    <h4 class="font-semibold text-gray-700">📱 响应式设计</h4>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• 移动端优先</li>
                        <li>• 自适应布局</li>
                        <li>• 触摸友好交互</li>
                        <li>• 跨设备兼容</li>
                    </ul>
                </div>
                <div class="space-y-2">
                    <h4 class="font-semibold text-gray-700">⚡ 技术特性</h4>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• Tailwind CSS</li>
                        <li>• FontAwesome 图标</li>
                        <li>• Alpine.js 交互</li>
                        <li>• 模块化组件</li>
                    </ul>
                </div>
            </div>
        </div>
    </main>

    <!-- 自定义脚本 -->
    <script src="assets/main.js"></script>

    <script>
        function showPage(pageId) {
            // 平滑滚动到对应页面
            const section = document.getElementById(pageId + '-section');
            if (section) {
                section.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 添加滚动效果
            const sections = document.querySelectorAll('.page-section');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                        entry.target.classList.add('animate-fade-in-up');
                    }
                });
            }, { threshold: 0.1 });

            sections.forEach(section => {
                section.style.opacity = '0';
                section.style.transform = 'translateY(20px)';
                section.style.transition = 'all 0.6s ease';
                observer.observe(section);
            });

            // 添加页面切换动画
            const navButtons = document.querySelectorAll('[x-data] button');
            navButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // 添加点击动画效果
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                });
            });

            // 显示欢迎提示
            setTimeout(() => {
                Utils.showNotification('欢迎查看项目研究报告平台的高保真原型！', 'success', 5000);
            }, 1000);
        });
    </script>
</body>
</html>
