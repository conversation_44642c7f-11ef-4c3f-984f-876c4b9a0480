{% extends "base.html" %}

{% block title %}{{ report.project_name }} 交互式分析 - 项目调研报告平台{% endblock %}

{% block extra_css %}
<style>
    .analysis-iframe {
        border: none;
        width: 100%;
        min-height: 800px;
        border-radius: 0.5rem;
        background: white;
    }
    .loading-skeleton {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200px 100%;
        animation: shimmer 1.5s infinite;
    }
    @keyframes shimmer {
        0% { background-position: -200px 0; }
        100% { background-position: calc(200px + 100%) 0; }
    }
    .fullscreen-btn {
        position: fixed;
        bottom: 20px;
        right: 20px;
        z-index: 1000;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 50%;
        width: 56px;
        height: 56px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        transition: all 0.3s ease;
    }
    .fullscreen-btn:hover {
        transform: scale(1.1);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    }
    .fullscreen-mode {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        z-index: 9999;
        background: white;
    }
    .fullscreen-mode .analysis-iframe {
        height: 100vh;
        border-radius: 0;
    }
    .btn-gradient {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        transition: all 0.3s ease;
    }
    .btn-gradient:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
    }
</style>
{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- 页面头部 -->
    <div class="mb-8">
        <div class="flex items-center justify-between mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-800 mb-2">
                    <i class="fas fa-chart-bar text-blue-600 mr-3"></i>
                    <span id="projectName">{{ report.project_name }}</span> 交互式分析页面
                </h1>
                <p class="text-gray-600">项目的交互式数据分析和可视化展示</p>
            </div>
            <div class="flex space-x-3">
                <button onclick="goBack()" class="bg-white border border-gray-200 text-gray-600 px-4 py-2 rounded-xl hover:bg-gray-50 transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>
                    返回列表
                </button>
                <button onclick="exportAnalysis()" class="bg-white border border-gray-200 text-gray-600 px-4 py-2 rounded-xl hover:bg-gray-50 transition-colors">
                    <i class="fas fa-download mr-2"></i>
                    导出分析
                </button>
                <button onclick="shareAnalysis()" class="btn-gradient text-white px-4 py-2 rounded-xl hover:opacity-90 transition-opacity">
                    <i class="fas fa-share mr-2"></i>
                    分享页面
                </button>
            </div>
        </div>
        
        <!-- 加载状态 -->
        <div id="loadingState" class="text-center py-12">
            <div class="loading-spinner mx-auto mb-4"></div>
            <p class="text-gray-600">正在加载交互式分析页面...</p>
        </div>
    </div>

    <!-- HTML内容渲染区域 -->
    <div id="analysisContainer" class="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden hidden">
        <!-- 内容头部 -->
        <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-800">
                    <i class="fas fa-code mr-2"></i>
                    交互式分析内容
                </h3>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2 text-sm text-gray-500">
                        <i class="fas fa-file-code mr-1"></i>
                        <span>HTML 格式</span>
                    </div>
                    <div class="flex items-center space-x-2 text-sm text-gray-500">
                        <i class="fas fa-clock mr-1"></i>
                        <span>最后更新：<span id="lastUpdated">{{ report.updated_at[:10] if report.updated_at else '-' }}</span></span>
                    </div>
                    <button onclick="toggleFullscreen()" class="text-gray-400 hover:text-gray-600 transition-colors" title="全屏显示">
                        <i class="fas fa-expand"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- HTML内容iframe -->
        <div class="relative">
            <iframe id="analysisFrame" 
                    class="analysis-iframe"
                    sandbox="allow-scripts allow-same-origin allow-forms allow-popups">
            </iframe>
        </div>
    </div>

    <!-- 错误状态 -->
    <div id="errorState" class="bg-white rounded-2xl shadow-sm border border-gray-100 p-8 text-center hidden">
        <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">加载失败</h3>
        <p class="text-gray-600 mb-4">无法加载交互式分析页面</p>
        <button onclick="retryLoad()" class="btn-gradient text-white px-4 py-2 rounded-xl font-medium">
            <i class="fas fa-redo mr-2"></i>
            重新加载
        </button>
    </div>

    <!-- 全屏按钮 -->
    <button id="fullscreenBtn" onclick="toggleFullscreen()" class="fullscreen-btn hidden" title="全屏显示">
        <i class="fas fa-expand"></i>
    </button>
</div>
{% endblock %}

{% block extra_js %}
<script>
let isFullscreen = false;
let analysisFrame;
let analysisContainer;

document.addEventListener('DOMContentLoaded', function() {
    analysisFrame = document.getElementById('analysisFrame');
    analysisContainer = document.getElementById('analysisContainer');
    
    loadAnalysisData();
});

async function loadAnalysisData() {
    const reportId = '{{ report.id }}';
    
    try {
        // 显示加载状态
        showLoadingState();
        
        // 直接使用分析内容
        {% if analysis_content %}
        analysisFrame.srcdoc = `{{ analysis_content | e }}`;
        {% else %}
        throw new Error('No analysis content available');
        {% endif %}
        
        // 监听iframe加载完成
        analysisFrame.onload = function() {
            showContent();
            adjustIframeHeight();
        };
        
        analysisFrame.onerror = function() {
            throw new Error('Failed to load iframe content');
        };
        
    } catch (error) {
        console.error('加载分析数据失败:', error);
        showErrorState();
    }
}

function adjustIframeHeight() {
    try {
        // 尝试获取iframe内容高度并调整
        const iframeDoc = analysisFrame.contentDocument || analysisFrame.contentWindow.document;
        const height = Math.max(
            iframeDoc.body.scrollHeight,
            iframeDoc.body.offsetHeight,
            iframeDoc.documentElement.clientHeight,
            iframeDoc.documentElement.scrollHeight,
            iframeDoc.documentElement.offsetHeight,
            800 // 最小高度
        );
        
        analysisFrame.style.height = height + 'px';
    } catch (e) {
        // 跨域限制，使用默认高度
        console.log('无法访问iframe内容，使用默认高度');
        analysisFrame.style.height = '800px';
    }
}

function showLoadingState() {
    document.getElementById('loadingState').classList.remove('hidden');
    document.getElementById('analysisContainer').classList.add('hidden');
    document.getElementById('errorState').classList.add('hidden');
    document.getElementById('fullscreenBtn').classList.add('hidden');
}

function showContent() {
    document.getElementById('loadingState').classList.add('hidden');
    document.getElementById('analysisContainer').classList.remove('hidden');
    document.getElementById('errorState').classList.add('hidden');
    document.getElementById('fullscreenBtn').classList.remove('hidden');
}

function showErrorState() {
    document.getElementById('loadingState').classList.add('hidden');
    document.getElementById('analysisContainer').classList.add('hidden');
    document.getElementById('errorState').classList.remove('hidden');
    document.getElementById('fullscreenBtn').classList.add('hidden');
}

function retryLoad() {
    loadAnalysisData();
}

function goBack() {
    window.history.back();
}

function toggleFullscreen() {
    const fullscreenBtn = document.getElementById('fullscreenBtn');
    const icon = fullscreenBtn.querySelector('i');
    
    if (!isFullscreen) {
        // 进入全屏
        analysisContainer.classList.add('fullscreen-mode');
        icon.className = 'fas fa-compress';
        fullscreenBtn.title = '退出全屏';
        isFullscreen = true;
        
        // 调整iframe高度为100vh
        analysisFrame.style.height = '100vh';
    } else {
        // 退出全屏
        analysisContainer.classList.remove('fullscreen-mode');
        icon.className = 'fas fa-expand';
        fullscreenBtn.title = '全屏显示';
        isFullscreen = false;
        
        // 恢复iframe高度
        adjustIframeHeight();
    }
}

function exportAnalysis() {
    // 创建导出链接
    const reportId = '{{ report.id }}';
    const exportUrl = `{{ url_for('public.export_analysis', report_id='') }}${reportId}`;
    
    const link = document.createElement('a');
    link.href = exportUrl;
    link.download = `{{ report.project_name }}_分析页面.html`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    showNotification('分析页面导出已开始', 'success');
}

function shareAnalysis() {
    const url = window.location.href;
    
    if (navigator.share) {
        navigator.share({
            title: '{{ report.project_name }} 交互式分析',
            text: '查看这个项目的交互式数据分析',
            url: url
        }).then(() => {
            showNotification('分享成功', 'success');
        }).catch(err => {
            console.log('分享失败:', err);
            copyToClipboard(url);
        });
    } else {
        copyToClipboard(url);
    }
}

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showNotification('链接已复制到剪贴板', 'success');
    }).catch(err => {
        console.error('复制失败:', err);
        showNotification('复制失败，请手动复制链接', 'error');
    });
}

// ESC键退出全屏
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape' && isFullscreen) {
        toggleFullscreen();
    }
});

// 监听窗口大小变化
window.addEventListener('resize', function() {
    if (!isFullscreen) {
        setTimeout(adjustIframeHeight, 100);
    }
});
</script>
{% endblock %}
