{% extends "base.html" %}

{% block title %}调研报告列表 - 项目调研报告平台{% endblock %}

{% block extra_css %}
<style>
    .search-container {
        position: relative;
    }
    .search-suggestions {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid #e5e7eb;
        border-radius: 0.5rem;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        z-index: 10;
        max-height: 200px;
        overflow-y: auto;
    }
    .table-hover tbody tr:hover {
        background-color: #f9fafb;
    }
    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 500;
    }
    .status-completed {
        background-color: #dcfce7;
        color: #166534;
    }
    .status-processing {
        background-color: #fef3c7;
        color: #92400e;
    }
    .status-pending {
        background-color: #dbeafe;
        color: #1e40af;
    }
    .btn-gradient {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        transition: all 0.3s ease;
    }
    .btn-gradient:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
    }
</style>
{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- 页面头部 -->
    <div class="mb-8">
        <div class="text-center mb-6">
            <h1 class="text-3xl font-bold text-gray-800 mb-2">
                <i class="fas fa-chart-line text-blue-600 mr-3"></i>
                项目调研报告
            </h1>
            <p class="text-gray-600">浏览已完成的项目调研报告，获取深度技术分析</p>
        </div>

        <!-- 搜索和操作栏 -->
        <div class="flex flex-col md:flex-row gap-4 items-center justify-between mb-6">
            <!-- 搜索框 -->
            <div class="flex-1 max-w-2xl search-container">
                <div class="relative">
                    <input type="text" 
                           id="searchInput"
                           placeholder="搜索项目名称..."
                           value="{{ request.args.get('search', '') }}"
                           class="w-full px-4 py-3 pl-12 pr-4 rounded-2xl border border-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all">
                    <i class="fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                </div>
                <div id="searchSuggestions" class="search-suggestions hidden"></div>
            </div>
            
            <!-- 找不到项目链接 -->
            <div class="flex items-center space-x-4">
                <button id="requestProjectBtn" 
                        class="text-blue-600 hover:text-blue-800 font-medium transition-colors">
                    <i class="fas fa-question-circle mr-2"></i>
                    找不到项目？
                </button>
            </div>
        </div>

        <!-- 提示信息 -->
        <div class="bg-blue-50 border border-blue-200 rounded-xl p-4 mb-6">
            <div class="flex items-start">
                <i class="fas fa-info-circle text-blue-600 mt-1 mr-3"></i>
                <div>
                    <p class="text-blue-800 text-sm">
                        请提交你想要调研的项目，算力因素报告产出可能会有延迟，完成后我们会通过邮箱通知，谢谢！
                    </p>
                </div>
            </div>
        </div>

        <!-- 统计信息 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div class="bg-white rounded-2xl p-4 shadow-sm border border-gray-100">
                <div class="flex items-center">
                    <div class="bg-blue-100 p-3 rounded-xl">
                        <i class="fas fa-file-alt text-blue-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">总报告数</p>
                        <p class="text-2xl font-bold text-gray-800">{{ total_reports }}</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-2xl p-4 shadow-sm border border-gray-100">
                <div class="flex items-center">
                    <div class="bg-green-100 p-3 rounded-xl">
                        <i class="fas fa-clock text-green-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">待处理请求</p>
                        <p class="text-2xl font-bold text-gray-800">{{ pending_requests }}</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-2xl p-4 shadow-sm border border-gray-100">
                <div class="flex items-center">
                    <div class="bg-purple-100 p-3 rounded-xl">
                        <i class="fas fa-star text-purple-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">本月新增</p>
                        <p class="text-2xl font-bold text-gray-800">{{ monthly_new }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 报告列表 -->
    <div class="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
        <!-- 表格头部 -->
        <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <h3 class="text-lg font-semibold text-gray-800">
                <i class="fas fa-list mr-2"></i>
                调研报告列表
            </h3>
        </div>

        <!-- 表格内容 -->
        <div class="overflow-x-auto">
            <table class="w-full table-hover">
                <thead class="bg-gray-50 border-b border-gray-200">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目名称</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建人</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">更新时间</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200" id="reportsTableBody">
                    {% for report in reports %}
                    <tr class="report-row" data-project-name="{{ report.project_name|lower }}">
                        <!-- 项目名称 -->
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    <div class="h-10 w-10 rounded-lg bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center">
                                        <i class="fas fa-project-diagram text-white text-sm"></i>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900">{{ report.project_name }}</div>
                                    <div class="text-sm text-gray-500">{{ report.category or '技术项目' }}</div>
                                </div>
                            </div>
                        </td>
                        
                        <!-- 创建人 -->
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="h-8 w-8 bg-gray-200 rounded-full flex items-center justify-center mr-2">
                                    <i class="fas fa-user text-gray-600 text-xs"></i>
                                </div>
                                <span class="text-sm text-gray-900">{{ report.created_by or '系统' }}</span>
                            </div>
                        </td>
                        
                        <!-- 创建时间 -->
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            <div class="flex items-center">
                                <i class="fas fa-calendar-plus mr-2 text-gray-400"></i>
                                <span>{{ report.created_at[:10] if report.created_at else '-' }}</span>
                            </div>
                        </td>

                        <!-- 更新时间 -->
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            <div class="flex items-center">
                                <i class="fas fa-calendar-check mr-2 text-gray-400"></i>
                                <span>{{ report.updated_at[:10] if report.updated_at else '-' }}</span>
                            </div>
                        </td>
                        
                        <!-- 状态 -->
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="status-badge status-{{ report.status or 'completed' }}">
                                <i class="fas fa-check-circle mr-1"></i>
                                {% if report.status == 'completed' %}已完成
                                {% elif report.status == 'processing' %}处理中
                                {% elif report.status == 'pending' %}待处理
                                {% else %}已完成{% endif %}
                            </span>
                        </td>
                        
                        <!-- 操作按钮 -->
                        <td class="px-6 py-4 whitespace-nowrap text-center">
                            <div class="flex items-center justify-center space-x-2">
                                {% if report.html_file_path %}
                                <button onclick="viewAnalysis('{{ report.id }}')" 
                                        class="bg-blue-50 text-blue-600 px-3 py-2 rounded-lg text-xs font-medium hover:bg-blue-100 transition-colors">
                                    <i class="fas fa-chart-bar mr-1"></i>
                                    查看分析页面
                                </button>
                                {% endif %}
                                {% if report.markdown_file_path %}
                                <button onclick="viewReport('{{ report.id }}')" 
                                        class="bg-gray-50 text-gray-600 px-3 py-2 rounded-lg text-xs font-medium hover:bg-gray-100 transition-colors">
                                    <i class="fas fa-file-alt mr-1"></i>
                                    查看分析报告
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- 空状态 -->
        {% if not reports %}
        <div class="text-center py-12">
            <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-search text-gray-400 text-xl"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">暂无调研报告</h3>
            <p class="text-gray-500 mb-4">还没有完成的调研报告，请稍后再来查看</p>
            <button id="requestProjectBtnEmpty" 
                    class="btn-gradient text-white px-4 py-2 rounded-xl font-medium">
                <i class="fas fa-plus mr-2"></i>
                申请新项目
            </button>
        </div>
        {% endif %}

        <!-- 无搜索结果状态 -->
        <div id="noSearchResults" class="text-center py-12 hidden">
            <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-search text-gray-400 text-xl"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">未找到相关报告</h3>
            <p class="text-gray-500 mb-4">尝试调整搜索关键词或提交新的项目申请</p>
            <button id="requestProjectBtnNoResults" 
                    class="btn-gradient text-white px-4 py-2 rounded-xl font-medium">
                <i class="fas fa-plus mr-2"></i>
                申请新项目
            </button>
        </div>
    </div>

    <!-- 分页 -->
    {% if pagination and pagination.pages > 1 %}
    <div class="flex justify-center mt-8">
        <div class="flex items-center space-x-2">
            {% if pagination.has_prev %}
            <a href="{{ url_for('public.reports_list', page=pagination.prev_num, search=request.args.get('search', '')) }}" 
               class="px-3 py-2 rounded-lg border border-gray-200 text-gray-600 hover:bg-gray-50 transition-colors">
                <i class="fas fa-chevron-left"></i>
            </a>
            {% endif %}
            
            {% for page_num in pagination.iter_pages() %}
                {% if page_num %}
                    {% if page_num != pagination.page %}
                    <a href="{{ url_for('public.reports_list', page=page_num, search=request.args.get('search', '')) }}" 
                       class="px-4 py-2 rounded-lg border border-gray-200 text-gray-600 hover:bg-gray-50 transition-colors">{{ page_num }}</a>
                    {% else %}
                    <span class="px-4 py-2 rounded-lg bg-blue-600 text-white font-medium">{{ page_num }}</span>
                    {% endif %}
                {% else %}
                <span class="px-4 py-2">…</span>
                {% endif %}
            {% endfor %}
            
            {% if pagination.has_next %}
            <a href="{{ url_for('public.reports_list', page=pagination.next_num, search=request.args.get('search', '')) }}" 
               class="px-3 py-2 rounded-lg border border-gray-200 text-gray-600 hover:bg-gray-50 transition-colors">
                <i class="fas fa-chevron-right"></i>
            </a>
            {% endif %}
        </div>
    </div>
    {% endif %}
</div>
<!-- 项目申请弹窗 -->
<div id="requestModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 hidden">
    <div class="bg-white rounded-2xl shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        <!-- 弹窗头部 -->
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900">
                    <i class="fas fa-plus-circle text-blue-600 mr-2"></i>
                    申请新项目调研
                </h3>
                <button id="closeModalBtn" class="text-gray-400 hover:text-gray-600 transition-colors">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>

        <!-- 弹窗内容 -->
        <form id="requestForm" class="p-6 space-y-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    您的邮箱 <span class="text-red-500">*</span>
                </label>
                <input type="email"
                       id="userEmail"
                       name="email"
                       placeholder="<EMAIL>"
                       required
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    项目名称 <span class="text-red-500">*</span>
                </label>
                <input type="text"
                       id="projectName"
                       name="project_name"
                       placeholder="例如：React.js"
                       required
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    官网主页链接 <span class="text-red-500">*</span>
                </label>
                <input type="url"
                       id="projectWebsite"
                       name="official_website"
                       placeholder="https://example.com"
                       required
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    补充说明 <span class="text-gray-500">(可选)</span>
                </label>
                <textarea id="projectDescription"
                          name="description"
                          placeholder="请简单描述您希望我们重点关注的方面..."
                          rows="3"
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"></textarea>
            </div>

            <!-- 提交按钮 -->
            <div class="flex space-x-3 pt-4">
                <button type="button"
                        id="cancelBtn"
                        class="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                    取消
                </button>
                <button type="submit"
                        id="submitBtn"
                        class="flex-1 btn-gradient text-white px-4 py-2 rounded-lg font-medium">
                    提交申请
                </button>
            </div>
        </form>
    </div>
</div>

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 获取DOM元素
    const searchInput = document.getElementById('searchInput');
    const searchSuggestions = document.getElementById('searchSuggestions');
    const requestModal = document.getElementById('requestModal');
    const requestForm = document.getElementById('requestForm');
    const reportsTableBody = document.getElementById('reportsTableBody');
    const noSearchResults = document.getElementById('noSearchResults');

    // 获取所有触发弹窗的按钮
    const requestBtns = [
        document.getElementById('requestProjectBtn'),
        document.getElementById('requestProjectBtnEmpty'),
        document.getElementById('requestProjectBtnNoResults')
    ].filter(btn => btn !== null);

    const closeModalBtn = document.getElementById('closeModalBtn');
    const cancelBtn = document.getElementById('cancelBtn');
    const submitBtn = document.getElementById('submitBtn');

    // 搜索功能
    let searchTimeout;
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            performSearch(this.value.trim());
        }, 300);
    });

    function performSearch(query) {
        const rows = reportsTableBody.querySelectorAll('.report-row');
        let visibleCount = 0;

        rows.forEach(row => {
            const projectName = row.dataset.projectName;
            if (!query || projectName.includes(query.toLowerCase())) {
                row.style.display = '';
                visibleCount++;
            } else {
                row.style.display = 'none';
            }
        });

        // 显示/隐藏无结果提示
        if (query && visibleCount === 0) {
            noSearchResults.classList.remove('hidden');
            reportsTableBody.parentElement.style.display = 'none';
        } else {
            noSearchResults.classList.add('hidden');
            reportsTableBody.parentElement.style.display = '';
        }

        // 更新URL
        const url = new URL(window.location);
        if (query) {
            url.searchParams.set('search', query);
        } else {
            url.searchParams.delete('search');
        }
        window.history.replaceState({}, '', url);
    }

    // 弹窗控制
    requestBtns.forEach(btn => {
        btn.addEventListener('click', openModal);
    });

    closeModalBtn.addEventListener('click', closeModal);
    cancelBtn.addEventListener('click', closeModal);

    // 点击弹窗外部关闭
    requestModal.addEventListener('click', function(e) {
        if (e.target === this) {
            closeModal();
        }
    });

    // ESC键关闭弹窗
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && !requestModal.classList.contains('hidden')) {
            closeModal();
        }
    });

    function openModal() {
        requestModal.classList.remove('hidden');
        document.body.style.overflow = 'hidden';
        document.getElementById('userEmail').focus();
    }

    function closeModal() {
        requestModal.classList.add('hidden');
        document.body.style.overflow = '';
        requestForm.reset();
    }

    // 表单提交
    requestForm.addEventListener('submit', async function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const data = Object.fromEntries(formData);

        // 显示加载状态
        const originalContent = showLoading(submitBtn);

        try {
            const response = await fetch('{{ url_for("public.request_project") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': '{{ csrf_token() }}'
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            if (response.ok) {
                showNotification('申请提交成功！我们会尽快处理您的申请。', 'success');
                closeModal();
            } else {
                showNotification(result.error || '提交失败，请稍后重试', 'error');
            }
        } catch (error) {
            console.error('提交错误:', error);
            showNotification('网络错误，请检查网络连接后重试', 'error');
        } finally {
            hideLoading(submitBtn, originalContent);
        }
    });
});

// 查看分析页面
function viewAnalysis(reportId) {
    window.open(`{{ url_for('public.analysis', report_id='') }}${reportId}`, '_blank');
}

// 查看分析报告
function viewReport(reportId) {
    window.open(`{{ url_for('public.report_detail', report_id='') }}${reportId}`, '_blank');
}
</script>
{% endblock %}
{% endblock %}
