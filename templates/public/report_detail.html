{% extends "base.html" %}

{% block title %}{{ report.project_name }} 调研报告 - 项目调研报告平台{% endblock %}

{% block extra_css %}
<style>
    .markdown-content {
        line-height: 1.8;
        color: #374151;
    }
    .markdown-content h1 {
        font-size: 2rem;
        font-weight: 700;
        margin: 2rem 0 1rem 0;
        color: #1f2937;
        border-bottom: 2px solid #e5e7eb;
        padding-bottom: 0.5rem;
    }
    .markdown-content h2 {
        font-size: 1.5rem;
        font-weight: 600;
        margin: 1.5rem 0 0.75rem 0;
        color: #374151;
    }
    .markdown-content h3 {
        font-size: 1.25rem;
        font-weight: 600;
        margin: 1.25rem 0 0.5rem 0;
        color: #4b5563;
    }
    .markdown-content p {
        margin: 1rem 0;
    }
    .markdown-content ul, .markdown-content ol {
        margin: 1rem 0;
        padding-left: 2rem;
    }
    .markdown-content li {
        margin: 0.5rem 0;
    }
    .markdown-content blockquote {
        border-left: 4px solid #3b82f6;
        background: #eff6ff;
        padding: 1rem 1.5rem;
        margin: 1.5rem 0;
        border-radius: 0.5rem;
    }
    .markdown-content code {
        background: #f3f4f6;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 0.875rem;
    }
    .markdown-content pre {
        background: #1f2937;
        color: #f9fafb;
        padding: 1.5rem;
        border-radius: 0.5rem;
        overflow-x: auto;
        margin: 1.5rem 0;
    }
    .markdown-content pre code {
        background: none;
        padding: 0;
        color: inherit;
    }
    .markdown-content table {
        width: 100%;
        border-collapse: collapse;
        margin: 1.5rem 0;
    }
    .markdown-content th, .markdown-content td {
        border: 1px solid #e5e7eb;
        padding: 0.75rem;
        text-align: left;
    }
    .markdown-content th {
        background: #f9fafb;
        font-weight: 600;
    }
    .loading-skeleton {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200px 100%;
        animation: shimmer 1.5s infinite;
    }
    @keyframes shimmer {
        0% { background-position: -200px 0; }
        100% { background-position: calc(200px + 100%) 0; }
    }
</style>
{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- 返回按钮 -->
    <div class="mb-6">
        <button onclick="goBack()" class="flex items-center text-gray-600 hover:text-gray-800 transition-colors">
            <i class="fas fa-arrow-left mr-2"></i>
            返回报告列表
        </button>
    </div>

    <!-- 加载状态 -->
    <div id="loadingState" class="space-y-4">
        <div class="loading-skeleton h-8 w-3/4 rounded"></div>
        <div class="loading-skeleton h-4 w-1/2 rounded"></div>
        <div class="loading-skeleton h-64 w-full rounded"></div>
    </div>

    <!-- 报告头部 -->
    <div id="reportHeader" class="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden mb-6 hidden">
        <!-- 项目封面 -->
        <div class="h-48 bg-gradient-to-br from-blue-400 to-purple-500 relative">
            <div class="absolute inset-0 bg-black bg-opacity-30 flex items-end">
                <div class="p-8 text-white">
                    <h1 class="text-3xl font-bold mb-2" id="reportTitle">{{ report.project_name }} 调研报告</h1>
                    <p class="text-lg opacity-90">深度技术分析与评估报告</p>
                </div>
            </div>
        </div>

        <!-- 项目信息 -->
        <div class="p-6">
            <div class="flex flex-wrap items-center justify-between">
                <div class="flex items-center space-x-4 mb-4 md:mb-0">
                    <div class="flex items-center text-sm text-gray-600">
                        <i class="fas fa-user mr-2"></i>
                        <span>研究员：<span id="reportCreator">{{ report.created_by or '系统' }}</span></span>
                    </div>
                    <div class="flex items-center text-sm text-gray-600">
                        <i class="fas fa-calendar mr-2"></i>
                        <span>创建时间：<span id="reportCreatedAt">{{ report.created_at[:10] if report.created_at else '-' }}</span></span>
                    </div>
                    <div class="flex items-center text-sm text-gray-600">
                        <i class="fas fa-clock mr-2"></i>
                        <span>更新时间：<span id="reportUpdatedAt">{{ report.updated_at[:10] if report.updated_at else '-' }}</span></span>
                    </div>
                </div>
                <div class="flex space-x-2">
                    {% if report.html_file_path %}
                    <button onclick="viewAnalysis('{{ report.id }}')" class="btn-gradient text-white px-4 py-2 rounded-xl font-medium">
                        <i class="fas fa-chart-bar mr-2"></i>
                        查看分析页面
                    </button>
                    {% endif %}
                    <button onclick="downloadReport()" class="bg-gray-100 text-gray-600 px-4 py-2 rounded-xl font-medium hover:bg-gray-200 transition-colors">
                        <i class="fas fa-download mr-2"></i>
                        下载报告
                    </button>
                    <button onclick="shareReport()" class="bg-gray-100 text-gray-600 px-4 py-2 rounded-xl font-medium hover:bg-gray-200 transition-colors">
                        <i class="fas fa-share mr-2"></i>
                        分享
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 报告内容 -->
    <div id="reportContent" class="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden hidden">
        <!-- 内容头部 -->
        <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-800">
                    <i class="fas fa-file-alt mr-2"></i>
                    调研报告内容
                </h3>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2 text-sm text-gray-500">
                        <i class="fas fa-file-code mr-1"></i>
                        <span>Markdown 格式</span>
                    </div>
                    <div class="flex items-center space-x-2 text-sm text-gray-500">
                        <i class="fas fa-clock mr-1"></i>
                        <span>最后更新：<span id="lastUpdated">{{ report.updated_at[:10] if report.updated_at else '-' }}</span></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Markdown内容渲染区域 -->
        <div class="p-8">
            <div id="markdownContent" class="markdown-content">
                <!-- Markdown内容将在这里渲染 -->
            </div>
        </div>
    </div>

    <!-- 错误状态 -->
    <div id="errorState" class="bg-white rounded-2xl shadow-sm border border-gray-100 p-8 text-center hidden">
        <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">加载失败</h3>
        <p class="text-gray-600 mb-4">无法加载调研报告内容</p>
        <button onclick="retryLoad()" class="btn-gradient text-white px-4 py-2 rounded-xl font-medium">
            <i class="fas fa-redo mr-2"></i>
            重新加载
        </button>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- 引入Markdown解析库 -->
<script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    loadReportData();
});

async function loadReportData() {
    const reportId = '{{ report.id }}';
    
    try {
        // 显示加载状态
        showLoadingState();
        
        // 加载报告基本信息（如果需要）
        // const reportResponse = await fetch(`/api/report/${reportId}`);
        // const reportData = await reportResponse.json();
        
        // 加载Markdown内容
        const markdownResponse = await fetch(`{{ url_for('public.get_markdown', report_id=report.id) }}`);
        
        if (!markdownResponse.ok) {
            throw new Error('Failed to load markdown content');
        }
        
        const markdownText = await markdownResponse.text();
        
        // 解析并渲染Markdown
        const htmlContent = marked.parse(markdownText);
        document.getElementById('markdownContent').innerHTML = htmlContent;
        
        // 显示内容
        showContent();
        
    } catch (error) {
        console.error('加载报告失败:', error);
        showErrorState();
    }
}

function showLoadingState() {
    document.getElementById('loadingState').classList.remove('hidden');
    document.getElementById('reportHeader').classList.add('hidden');
    document.getElementById('reportContent').classList.add('hidden');
    document.getElementById('errorState').classList.add('hidden');
}

function showContent() {
    document.getElementById('loadingState').classList.add('hidden');
    document.getElementById('reportHeader').classList.remove('hidden');
    document.getElementById('reportContent').classList.remove('hidden');
    document.getElementById('errorState').classList.add('hidden');
}

function showErrorState() {
    document.getElementById('loadingState').classList.add('hidden');
    document.getElementById('reportHeader').classList.add('hidden');
    document.getElementById('reportContent').classList.add('hidden');
    document.getElementById('errorState').classList.remove('hidden');
}

function retryLoad() {
    loadReportData();
}

function goBack() {
    window.history.back();
}

function viewAnalysis(reportId) {
    window.open(`{{ url_for('public.analysis', report_id='') }}${reportId}`, '_blank');
}

function downloadReport() {
    // 创建下载链接
    const reportId = '{{ report.id }}';
    const downloadUrl = `{{ url_for('public.download_report', report_id='') }}${reportId}`;
    
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = `{{ report.project_name }}_调研报告.md`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    showNotification('报告下载已开始', 'success');
}

function shareReport() {
    const url = window.location.href;
    
    if (navigator.share) {
        navigator.share({
            title: '{{ report.project_name }} 调研报告',
            text: '查看这个项目的深度调研报告',
            url: url
        }).then(() => {
            showNotification('分享成功', 'success');
        }).catch(err => {
            console.log('分享失败:', err);
            copyToClipboard(url);
        });
    } else {
        copyToClipboard(url);
    }
}

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showNotification('链接已复制到剪贴板', 'success');
    }).catch(err => {
        console.error('复制失败:', err);
        showNotification('复制失败，请手动复制链接', 'error');
    });
}
</script>
{% endblock %}
