// 图标组件系统 - 统一管理项目中的所有图标

// 图标映射表 - 将语义化名称映射到具体的图标类或SVG
const ICON_MAP = {
    // 基础操作图标
    'search': 'fas fa-search',
    'add': 'fas fa-plus',
    'edit': 'fas fa-edit',
    'delete': 'fas fa-trash',
    'save': 'fas fa-save',
    'cancel': 'fas fa-times',
    'close': 'fas fa-times',
    'back': 'fas fa-arrow-left',
    'forward': 'fas fa-arrow-right',
    'up': 'fas fa-arrow-up',
    'down': 'fas fa-arrow-down',
    'refresh': 'fas fa-sync-alt',
    'loading': 'fas fa-spinner fa-spin',
    
    // 导航图标
    'home': 'fas fa-home',
    'dashboard': 'fas fa-tachometer-alt',
    'menu': 'fas fa-bars',
    'user': 'fas fa-user',
    'settings': 'fas fa-cog',
    'logout': 'fas fa-sign-out-alt',
    'login': 'fas fa-sign-in-alt',
    
    // 内容图标
    'document': 'fas fa-file-alt',
    'report': 'fas fa-chart-bar',
    'project': 'fas fa-folder-open',
    'analysis': 'fas fa-analytics',
    'chart': 'fas fa-chart-line',
    'table': 'fas fa-table',
    'image': 'fas fa-image',
    'download': 'fas fa-download',
    'upload': 'fas fa-upload',
    'link': 'fas fa-external-link-alt',
    
    // 状态图标
    'success': 'fas fa-check-circle',
    'error': 'fas fa-exclamation-circle',
    'warning': 'fas fa-exclamation-triangle',
    'info': 'fas fa-info-circle',
    'pending': 'fas fa-clock',
    'completed': 'fas fa-check',
    
    // 社交和通信图标
    'email': 'fas fa-envelope',
    'phone': 'fas fa-phone',
    'website': 'fas fa-globe',
    'github': 'fab fa-github',
    'twitter': 'fab fa-twitter',
    'linkedin': 'fab fa-linkedin',
    
    // 主题和界面图标
    'light': 'fas fa-sun',
    'dark': 'fas fa-moon',
    'theme': 'fas fa-palette',
    'fullscreen': 'fas fa-expand',
    'minimize': 'fas fa-compress',
    
    // 数据和统计图标
    'trending-up': 'fas fa-trending-up',
    'trending-down': 'fas fa-trending-down',
    'calendar': 'fas fa-calendar',
    'clock': 'fas fa-clock',
    'star': 'fas fa-star',
    'heart': 'fas fa-heart',
    'bookmark': 'fas fa-bookmark',
    
    // 技术相关图标
    'code': 'fas fa-code',
    'terminal': 'fas fa-terminal',
    'database': 'fas fa-database',
    'server': 'fas fa-server',
    'cloud': 'fas fa-cloud',
    'api': 'fas fa-plug',
    'security': 'fas fa-shield-alt',
    'bug': 'fas fa-bug'
};

// SVG图标定义 (Heroicons风格)
const SVG_ICONS = {
    'search': `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
    </svg>`,
    
    'add': `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
    </svg>`,
    
    'chart': `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
    </svg>`,
    
    'document': `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
    </svg>`,
    
    'user': `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
    </svg>`,
    
    'settings': `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
    </svg>`,
    
    'close': `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
    </svg>`,
    
    'success': `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
    </svg>`,
    
    'error': `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
    </svg>`,
    
    'warning': `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
    </svg>`,
    
    'info': `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
    </svg>`,
    
    'external-link': `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
    </svg>`
};

// 图标组件类
class IconComponent {
    constructor() {
        this.preferSVG = true; // 优先使用SVG图标
    }
    
    // 获取图标HTML
    getIcon(name, options = {}) {
        const {
            size = 'w-5 h-5',
            className = '',
            style = '',
            useSVG = this.preferSVG
        } = options;
        
        if (useSVG && SVG_ICONS[name]) {
            return SVG_ICONS[name].replace('w-5 h-5', size);
        }
        
        if (ICON_MAP[name]) {
            return `<i class="${ICON_MAP[name]} ${className}" style="${style}"></i>`;
        }
        
        // 回退到默认图标
        return `<i class="fas fa-question-circle ${className}" style="${style}"></i>`;
    }
    
    // 创建图标元素
    createElement(name, options = {}) {
        const iconHTML = this.getIcon(name, options);
        const wrapper = document.createElement('span');
        wrapper.innerHTML = iconHTML;
        return wrapper.firstChild;
    }
    
    // 替换页面中的图标占位符
    replaceIcons() {
        document.querySelectorAll('[data-icon]').forEach(element => {
            const iconName = element.getAttribute('data-icon');
            const size = element.getAttribute('data-icon-size') || 'w-5 h-5';
            const className = element.getAttribute('data-icon-class') || '';
            
            const iconHTML = this.getIcon(iconName, { size, className });
            element.innerHTML = iconHTML;
        });
    }
}

// 全局图标实例
window.IconComponent = new IconComponent();

// 页面加载完成后替换图标
document.addEventListener('DOMContentLoaded', function() {
    window.IconComponent.replaceIcons();
});

// jQuery插件形式
if (typeof jQuery !== 'undefined') {
    jQuery.fn.setIcon = function(iconName, options = {}) {
        return this.each(function() {
            const iconHTML = window.IconComponent.getIcon(iconName, options);
            $(this).html(iconHTML);
        });
    };
}
