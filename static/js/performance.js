// 性能监控和优化脚本

class PerformanceMonitor {
    constructor() {
        this.metrics = {};
        this.observers = {};
        this.init();
    }

    init() {
        this.measurePageLoad();
        this.setupIntersectionObserver();
        this.setupResizeObserver();
        this.monitorLCP();
        this.monitorFID();
        this.monitorCLS();
        this.optimizeImages();
        this.preloadCriticalResources();
    }

    // 页面加载性能测量
    measurePageLoad() {
        window.addEventListener('load', () => {
            const navigation = performance.getEntriesByType('navigation')[0];
            const paint = performance.getEntriesByType('paint');
            
            this.metrics.pageLoad = {
                domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
                loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
                firstPaint: paint.find(p => p.name === 'first-paint')?.startTime || 0,
                firstContentfulPaint: paint.find(p => p.name === 'first-contentful-paint')?.startTime || 0,
                totalLoadTime: navigation.loadEventEnd - navigation.fetchStart
            };

            this.reportMetrics();
        });
    }

    // 最大内容绘制 (LCP) 监控
    monitorLCP() {
        if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                const lastEntry = entries[entries.length - 1];
                this.metrics.lcp = lastEntry.startTime;
            });
            
            observer.observe({ entryTypes: ['largest-contentful-paint'] });
            this.observers.lcp = observer;
        }
    }

    // 首次输入延迟 (FID) 监控
    monitorFID() {
        if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                entries.forEach(entry => {
                    this.metrics.fid = entry.processingStart - entry.startTime;
                });
            });
            
            observer.observe({ entryTypes: ['first-input'] });
            this.observers.fid = observer;
        }
    }

    // 累积布局偏移 (CLS) 监控
    monitorCLS() {
        if ('PerformanceObserver' in window) {
            let clsValue = 0;
            const observer = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                entries.forEach(entry => {
                    if (!entry.hadRecentInput) {
                        clsValue += entry.value;
                    }
                });
                this.metrics.cls = clsValue;
            });
            
            observer.observe({ entryTypes: ['layout-shift'] });
            this.observers.cls = observer;
        }
    }

    // 交叉观察器设置 - 懒加载优化
    setupIntersectionObserver() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        if (img.dataset.src) {
                            img.src = img.dataset.src;
                            img.removeAttribute('data-src');
                            imageObserver.unobserve(img);
                        }
                    }
                });
            }, {
                rootMargin: '50px 0px',
                threshold: 0.01
            });

            // 观察所有懒加载图片
            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });

            this.observers.intersection = imageObserver;
        }
    }

    // 尺寸变化观察器
    setupResizeObserver() {
        if ('ResizeObserver' in window) {
            const resizeObserver = new ResizeObserver(entries => {
                // 防抖处理
                clearTimeout(this.resizeTimeout);
                this.resizeTimeout = setTimeout(() => {
                    this.handleResize();
                }, 250);
            });

            resizeObserver.observe(document.body);
            this.observers.resize = resizeObserver;
        }
    }

    // 处理窗口大小变化
    handleResize() {
        // 重新计算布局相关的性能指标
        this.optimizeViewport();
    }

    // 视口优化
    optimizeViewport() {
        const viewport = {
            width: window.innerWidth,
            height: window.innerHeight,
            devicePixelRatio: window.devicePixelRatio || 1
        };

        // 根据视口大小调整资源加载策略
        if (viewport.width < 768) {
            this.enableMobileOptimizations();
        } else {
            this.enableDesktopOptimizations();
        }
    }

    // 移动端优化
    enableMobileOptimizations() {
        // 减少动画
        document.body.classList.add('mobile-optimized');
        
        // 延迟加载非关键资源
        this.deferNonCriticalResources();
        
        // 压缩图片质量
        this.optimizeImagesForMobile();
    }

    // 桌面端优化
    enableDesktopOptimizations() {
        document.body.classList.remove('mobile-optimized');
        
        // 预加载可能需要的资源
        this.preloadDesktopResources();
    }

    // 图片优化
    optimizeImages() {
        const images = document.querySelectorAll('img');
        images.forEach(img => {
            // 添加懒加载
            if (!img.hasAttribute('loading')) {
                img.setAttribute('loading', 'lazy');
            }

            // 添加解码提示
            if (!img.hasAttribute('decoding')) {
                img.setAttribute('decoding', 'async');
            }

            // 错误处理
            img.addEventListener('error', () => {
                img.style.display = 'none';
            });
        });
    }

    // 移动端图片优化
    optimizeImagesForMobile() {
        const images = document.querySelectorAll('img');
        images.forEach(img => {
            if (img.src && !img.dataset.originalSrc) {
                img.dataset.originalSrc = img.src;
                // 可以在这里实现图片压缩逻辑
            }
        });
    }

    // 预加载关键资源
    preloadCriticalResources() {
        const criticalResources = [
            '/static/css/custom.css',
            '/static/js/main.js'
        ];

        criticalResources.forEach(resource => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.href = resource;
            link.as = resource.endsWith('.css') ? 'style' : 'script';
            document.head.appendChild(link);
        });
    }

    // 桌面端资源预加载
    preloadDesktopResources() {
        // 预加载可能访问的页面
        const importantLinks = document.querySelectorAll('a[href^="/"]');
        importantLinks.forEach(link => {
            if (link.href && !link.dataset.preloaded) {
                const prefetchLink = document.createElement('link');
                prefetchLink.rel = 'prefetch';
                prefetchLink.href = link.href;
                document.head.appendChild(prefetchLink);
                link.dataset.preloaded = 'true';
            }
        });
    }

    // 延迟非关键资源
    deferNonCriticalResources() {
        // 延迟加载非关键CSS
        const nonCriticalCSS = document.querySelectorAll('link[rel="stylesheet"]:not([data-critical])');
        nonCriticalCSS.forEach(link => {
            link.media = 'print';
            link.onload = function() {
                this.media = 'all';
            };
        });
    }

    // 报告性能指标
    reportMetrics() {
        console.group('🚀 Performance Metrics');
        console.log('📊 Page Load:', this.metrics.pageLoad);
        console.log('🎨 LCP:', this.metrics.lcp + 'ms');
        console.log('⚡ FID:', this.metrics.fid + 'ms');
        console.log('📐 CLS:', this.metrics.cls);
        console.groupEnd();

        // 发送到分析服务（如果需要）
        this.sendToAnalytics();
    }

    // 发送分析数据
    sendToAnalytics() {
        // 这里可以集成 Google Analytics 或其他分析服务
        if (typeof gtag !== 'undefined') {
            gtag('event', 'performance_metrics', {
                'custom_map': {
                    'lcp': this.metrics.lcp,
                    'fid': this.metrics.fid,
                    'cls': this.metrics.cls
                }
            });
        }
    }

    // 清理观察器
    cleanup() {
        Object.values(this.observers).forEach(observer => {
            if (observer && observer.disconnect) {
                observer.disconnect();
            }
        });
    }
}

// 初始化性能监控
const performanceMonitor = new PerformanceMonitor();

// 页面卸载时清理
window.addEventListener('beforeunload', () => {
    performanceMonitor.cleanup();
});

// 导出供其他模块使用
window.PerformanceMonitor = PerformanceMonitor;
