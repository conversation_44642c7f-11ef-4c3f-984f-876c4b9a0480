#!/usr/bin/env python3
"""
前端功能测试脚本
测试新开发的前端页面和API功能
"""

import requests
import json
import time
from datetime import datetime

BASE_URL = "http://127.0.0.1:5002"

def test_homepage():
    """测试首页加载"""
    print("🏠 测试首页...")
    try:
        response = requests.get(f"{BASE_URL}/")
        if response.status_code == 200:
            print("✅ 首页加载成功")
            # 检查是否包含关键元素
            if "项目调研报告" in response.text:
                print("✅ 页面标题正确")
            if "搜索项目名称" in response.text:
                print("✅ 搜索功能存在")
            if "申请新项目" in response.text:
                print("✅ 项目申请功能存在")
            return True
        else:
            print(f"❌ 首页加载失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 首页测试出错: {e}")
        return False

def test_search_functionality():
    """测试搜索功能"""
    print("\n🔍 测试搜索功能...")
    try:
        # 测试搜索API
        response = requests.get(f"{BASE_URL}/search?q=test")
        if response.status_code == 200:
            print("✅ 搜索API响应正常")
            data = response.json()
            print(f"📊 搜索结果: {data.get('total', 0)} 条记录")
            return True
        else:
            print(f"❌ 搜索API失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 搜索测试出错: {e}")
        return False

def test_project_request():
    """测试项目申请功能"""
    print("\n📝 测试项目申请...")
    try:
        test_data = {
            "email": "<EMAIL>",
            "project_name": f"测试项目_{int(time.time())}",
            "official_website": "https://github.com",
            "description": "这是一个测试项目申请"
        }
        
        response = requests.post(
            f"{BASE_URL}/request-project",
            json=test_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ 项目申请提交成功")
                return True
            else:
                print(f"❌ 项目申请失败: {result.get('message', '未知错误')}")
                return False
        else:
            print(f"❌ 项目申请API失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 项目申请测试出错: {e}")
        return False

def test_health_check():
    """测试健康检查"""
    print("\n💚 测试健康检查...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            data = response.json()
            status = data.get('status', 'unknown')
            print(f"✅ 健康检查: {status}")
            print(f"📊 数据库状态: {data.get('database', 'unknown')}")
            return status == 'healthy'
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 健康检查出错: {e}")
        return False

def test_report_pages():
    """测试报告页面"""
    print("\n📄 测试报告页面...")
    try:
        # 首先获取报告列表
        response = requests.get(f"{BASE_URL}/search?q=")
        if response.status_code == 200:
            data = response.json()
            reports = data.get('reports', [])
            
            if reports:
                # 测试第一个报告的详情页面
                report_id = reports[0].get('id')
                if report_id:
                    # 测试报告详情页面
                    detail_response = requests.get(f"{BASE_URL}/report/{report_id}")
                    if detail_response.status_code == 200:
                        print("✅ 报告详情页面加载成功")
                    else:
                        print(f"❌ 报告详情页面失败: {detail_response.status_code}")
                    
                    # 测试分析页面
                    analysis_response = requests.get(f"{BASE_URL}/report/{report_id}/analysis")
                    if analysis_response.status_code == 200:
                        print("✅ 分析页面加载成功")
                    else:
                        print(f"❌ 分析页面失败: {analysis_response.status_code}")
                    
                    return True
                else:
                    print("⚠️ 没有找到有效的报告ID")
                    return True  # 不算失败，可能是数据库为空
            else:
                print("⚠️ 没有找到报告数据")
                return True  # 不算失败，可能是数据库为空
        else:
            print(f"❌ 获取报告列表失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 报告页面测试出错: {e}")
        return False

def test_admin_login_page():
    """测试管理员登录页面"""
    print("\n🔐 测试管理员登录页面...")
    try:
        response = requests.get(f"{BASE_URL}/admin/login")
        if response.status_code == 200:
            print("✅ 管理员登录页面加载成功")
            if "管理员登录" in response.text or "登录" in response.text:
                print("✅ 登录页面内容正确")
            return True
        else:
            print(f"❌ 管理员登录页面失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 管理员登录页面测试出错: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("🚀 开始前端功能测试...")
    print("=" * 50)
    
    tests = [
        ("首页功能", test_homepage),
        ("搜索功能", test_search_functionality),
        ("项目申请", test_project_request),
        ("健康检查", test_health_check),
        ("报告页面", test_report_pages),
        ("管理员登录", test_admin_login_page),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15} {status}")
        if result:
            passed += 1
    
    print("=" * 50)
    print(f"总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！前端功能正常工作。")
    else:
        print("⚠️ 部分测试失败，请检查相关功能。")
    
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    exit(0 if success else 1)
