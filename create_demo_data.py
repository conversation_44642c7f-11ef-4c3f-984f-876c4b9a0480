#!/usr/bin/env python3
"""
创建演示数据脚本
为项目调研报告平台添加示例数据
"""

import os
import sys
from datetime import datetime, timedelta
import random

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.services.database import db_service
from app.models.research_report import ResearchReport
from app.models.user_request import UserRequest

def create_sample_reports():
    """创建示例调研报告"""
    print("📄 创建示例调研报告...")
    
    sample_reports = [
        {
            "project_name": "React.js",
            "official_website": "https://reactjs.org",
            "description": "用于构建用户界面的JavaScript库，由Facebook开发维护。React使用虚拟DOM技术，提供高效的组件化开发体验。",
            "creator_name": "系统管理员",
            "is_published": True,
            "report_file_path": "react_report.md",
            "analysis_file_path": "react_analysis.html"
        },
        {
            "project_name": "Vue.js",
            "official_website": "https://vuejs.org",
            "description": "渐进式JavaScript框架，易于上手，适合构建现代化的Web应用程序。Vue.js具有响应式数据绑定和组合式API。",
            "creator_name": "技术研究员",
            "is_published": True,
            "report_file_path": "vue_report.md",
            "analysis_file_path": "vue_analysis.html"
        },
        {
            "project_name": "Django",
            "official_website": "https://djangoproject.com",
            "description": "高级Python Web框架，鼓励快速开发和干净、实用的设计。Django遵循DRY原则，提供强大的ORM和管理界面。",
            "creator_name": "后端专家",
            "is_published": True,
            "report_file_path": "django_report.md",
            "analysis_file_path": "django_analysis.html"
        },
        {
            "project_name": "Docker",
            "official_website": "https://docker.com",
            "description": "容器化平台，使开发者能够将应用程序及其依赖项打包到轻量级、可移植的容器中。Docker简化了应用部署和扩展。",
            "creator_name": "运维工程师",
            "is_published": True,
            "report_file_path": "docker_report.md",
            "analysis_file_path": "docker_analysis.html"
        },
        {
            "project_name": "Kubernetes",
            "official_website": "https://kubernetes.io",
            "description": "开源容器编排平台，自动化容器化应用程序的部署、扩展和管理。K8s提供服务发现、负载均衡等企业级功能。",
            "creator_name": "云架构师",
            "is_published": True,
            "report_file_path": "k8s_report.md",
            "analysis_file_path": "k8s_analysis.html"
        }
    ]
    
    created_count = 0
    for report_data in sample_reports:
        try:
            # 添加时间戳
            base_time = datetime.now() - timedelta(days=random.randint(1, 30))
            report_data['created_at'] = base_time.isoformat()
            report_data['updated_at'] = (base_time + timedelta(hours=random.randint(1, 48))).isoformat()
            
            # 创建报告
            result = db_service.execute_query(
                'research_reports',
                'insert',
                data=report_data
            )
            
            if result.data:
                print(f"✅ 创建报告: {report_data['project_name']}")
                created_count += 1
            else:
                print(f"❌ 创建报告失败: {report_data['project_name']}")
                
        except Exception as e:
            print(f"❌ 创建报告出错 {report_data['project_name']}: {e}")
    
    print(f"📊 成功创建 {created_count} 个示例报告")
    return created_count

def create_sample_requests():
    """创建示例用户请求"""
    print("\n📝 创建示例用户请求...")
    
    sample_requests = [
        {
            "user_email": "<EMAIL>",
            "project_name": "Next.js",
            "official_website": "https://nextjs.org",
            "status": "pending"
        },
        {
            "user_email": "<EMAIL>",
            "project_name": "GraphQL",
            "official_website": "https://graphql.org",
            "status": "processing"
        },
        {
            "user_email": "<EMAIL>",
            "project_name": "Supabase",
            "official_website": "https://supabase.com",
            "status": "pending"
        },
        {
            "user_email": "<EMAIL>",
            "project_name": "TensorFlow",
            "official_website": "https://tensorflow.org",
            "status": "completed"
        }
    ]
    
    created_count = 0
    for request_data in sample_requests:
        try:
            # 添加时间戳
            base_time = datetime.now() - timedelta(days=random.randint(1, 15))
            request_data['created_at'] = base_time.isoformat()
            request_data['updated_at'] = (base_time + timedelta(hours=random.randint(1, 24))).isoformat()
            
            # 创建请求
            result = db_service.execute_query(
                'user_requests',
                'insert',
                data=request_data
            )
            
            if result.data:
                print(f"✅ 创建请求: {request_data['project_name']} ({request_data['user_email']})")
                created_count += 1
            else:
                print(f"❌ 创建请求失败: {request_data['project_name']}")
                
        except Exception as e:
            print(f"❌ 创建请求出错 {request_data['project_name']}: {e}")
    
    print(f"📊 成功创建 {created_count} 个示例请求")
    return created_count

def create_sample_files():
    """创建示例文件"""
    print("\n📁 创建示例文件...")
    
    # 确保目录存在
    reports_dir = "uploads/reports"
    analysis_dir = "uploads/analysis"
    
    os.makedirs(reports_dir, exist_ok=True)
    os.makedirs(analysis_dir, exist_ok=True)
    
    # 创建示例Markdown报告
    sample_markdown = """# {project_name} 技术调研报告

## 项目概述

{project_name} 是一个{description}

## 技术特点

### 核心优势
- 高性能和可扩展性
- 活跃的社区支持
- 丰富的生态系统
- 良好的文档和学习资源

### 技术架构
- 现代化的设计理念
- 模块化的组件结构
- 灵活的配置选项
- 强大的扩展能力

## 使用场景

{project_name} 适用于以下场景：
1. 企业级应用开发
2. 快速原型构建
3. 大规模系统架构
4. 现代化技术栈升级

## 学习建议

### 入门路径
1. 阅读官方文档
2. 完成入门教程
3. 参与社区项目
4. 实践项目开发

### 进阶学习
- 深入理解核心原理
- 学习最佳实践
- 参与开源贡献
- 关注技术发展趋势

## 总结

{project_name} 是一个值得学习和使用的优秀技术项目，具有广阔的应用前景和发展空间。

---
*报告生成时间: {timestamp}*
"""

    # 创建示例HTML分析页面
    sample_html = """<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{project_name} 数据分析</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }}
        .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        .chart-container {{ margin: 20px 0; padding: 20px; background: #fafafa; border-radius: 8px; }}
        h1 {{ color: #333; text-align: center; margin-bottom: 30px; }}
        h2 {{ color: #555; border-bottom: 2px solid #667eea; padding-bottom: 10px; }}
        .metric {{ display: inline-block; margin: 10px; padding: 15px; background: linear-gradient(135deg, #667eea, #764ba2); color: white; border-radius: 8px; text-align: center; min-width: 120px; }}
        .metric-value {{ font-size: 24px; font-weight: bold; }}
        .metric-label {{ font-size: 12px; opacity: 0.9; }}
    </style>
</head>
<body>
    <div class="container">
        <h1>{project_name} 技术分析报告</h1>
        
        <div style="text-align: center; margin: 20px 0;">
            <div class="metric">
                <div class="metric-value">{popularity}</div>
                <div class="metric-label">GitHub Stars</div>
            </div>
            <div class="metric">
                <div class="metric-value">{contributors}</div>
                <div class="metric-label">贡献者</div>
            </div>
            <div class="metric">
                <div class="metric-value">{downloads}</div>
                <div class="metric-label">月下载量</div>
            </div>
            <div class="metric">
                <div class="metric-value">{score}</div>
                <div class="metric-label">技术评分</div>
            </div>
        </div>

        <div class="chart-container">
            <h2>技术趋势分析</h2>
            <canvas id="trendChart" width="400" height="200"></canvas>
        </div>

        <div class="chart-container">
            <h2>社区活跃度</h2>
            <canvas id="activityChart" width="400" height="200"></canvas>
        </div>
    </div>

    <script>
        // 趋势图表
        const trendCtx = document.getElementById('trendChart').getContext('2d');
        new Chart(trendCtx, {{
            type: 'line',
            data: {{
                labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                datasets: [{{
                    label: 'GitHub Stars',
                    data: {trend_data},
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4
                }}]
            }},
            options: {{
                responsive: true,
                plugins: {{
                    legend: {{ position: 'top' }}
                }}
            }}
        }});

        // 活跃度图表
        const activityCtx = document.getElementById('activityChart').getContext('2d');
        new Chart(activityCtx, {{
            type: 'doughnut',
            data: {{
                labels: ['Issues', 'Pull Requests', 'Commits', 'Releases'],
                datasets: [{{
                    data: {activity_data},
                    backgroundColor: ['#ff6384', '#36a2eb', '#ffce56', '#4bc0c0']
                }}]
            }},
            options: {{
                responsive: true,
                plugins: {{
                    legend: {{ position: 'bottom' }}
                }}
            }}
        }});
    </script>
</body>
</html>"""

    projects = [
        {"name": "React.js", "file": "react", "popularity": "220k", "contributors": "1.5k", "downloads": "20M", "score": "9.2"},
        {"name": "Vue.js", "file": "vue", "popularity": "206k", "contributors": "440", "downloads": "4.2M", "score": "8.9"},
        {"name": "Django", "file": "django", "popularity": "75k", "contributors": "2.3k", "downloads": "2.1M", "score": "8.7"},
        {"name": "Docker", "file": "docker", "popularity": "68k", "contributors": "2.8k", "downloads": "1B+", "score": "9.5"},
        {"name": "Kubernetes", "file": "k8s", "popularity": "107k", "contributors": "3.2k", "downloads": "N/A", "score": "9.1"}
    ]
    
    created_files = 0
    
    for project in projects:
        try:
            # 创建Markdown文件
            md_content = sample_markdown.format(
                project_name=project["name"],
                description=f"优秀的开源项目",
                timestamp=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            )
            
            md_path = os.path.join(reports_dir, f"{project['file']}_report.md")
            with open(md_path, 'w', encoding='utf-8') as f:
                f.write(md_content)
            
            # 创建HTML文件
            html_content = sample_html.format(
                project_name=project["name"],
                popularity=project["popularity"],
                contributors=project["contributors"],
                downloads=project["downloads"],
                score=project["score"],
                trend_data=[random.randint(1000, 5000) for _ in range(6)],
                activity_data=[random.randint(50, 500) for _ in range(4)]
            )
            
            html_path = os.path.join(analysis_dir, f"{project['file']}_analysis.html")
            with open(html_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            print(f"✅ 创建文件: {project['name']}")
            created_files += 2
            
        except Exception as e:
            print(f"❌ 创建文件失败 {project['name']}: {e}")
    
    print(f"📊 成功创建 {created_files} 个示例文件")
    return created_files

def main():
    """主函数"""
    print("🚀 开始创建演示数据...")
    print("=" * 50)
    
    try:
        # 测试数据库连接
        if not db_service.test_connection():
            print("❌ 数据库连接失败，请检查配置")
            return False
        
        print("✅ 数据库连接成功")
        
        # 创建示例数据
        reports_count = create_sample_reports()
        requests_count = create_sample_requests()
        files_count = create_sample_files()
        
        print("\n" + "=" * 50)
        print("📊 演示数据创建完成!")
        print(f"📄 调研报告: {reports_count} 个")
        print(f"📝 用户请求: {requests_count} 个")
        print(f"📁 示例文件: {files_count} 个")
        print("\n🎉 现在可以访问 http://127.0.0.1:5002 查看演示效果!")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建演示数据失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
